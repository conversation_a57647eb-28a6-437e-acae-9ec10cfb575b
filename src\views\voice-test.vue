<script setup>
import { onMounted, onUnmounted, ref } from 'vue'

import XunfeiVoiceDictation from '../utils/xunfei-voice.js'

// 响应式数据
const appid = ref('5ec244d5')
const apisecret = ref('37912e3e3f205e2a6201ec290452470a')
const apikey = ref('78b6c006f1f3df5e24d315e3dff09212')
const status = ref('未初始化')
const statusClass = ref('idle')
const result = ref('等待开始录音...')
const logs = ref([])
const isRecording = ref(false)

// 语音实例
let voiceInstance = null

// 日志处理
function addLog(level, message, data = null) {
  const timestamp = new Date().toLocaleTimeString()
  let logText = `[${timestamp}] [${level.toUpperCase()}] ${message}`

  if (data) {
    logText += ` ${JSON.stringify(data)}`
  }

  logs.value.push({
    level,
    text: logText,
    timestamp,
  })

  // 保持日志数量在合理范围内
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(-50)
  }
}

// 更新状态显示
function updateStatus(newStatus, text) {
  status.value = text
  statusClass.value = newStatus
}

// 更新识别结果
function updateResult(text) {
  result.value = text || '等待识别结果...'
}

// 创建实例
function createInstance() {
  if (!appid.value || !apisecret.value || !apikey.value) {
    alert('请填写完整的API配置信息')
    return
  }

  try {
    voiceInstance = new XunfeiVoiceDictation({
      APPID: appid.value,
      APISecret: apisecret.value,
      APIKey: apikey.value,
      instanceId: 'vue_test_instance',
      logLevel: 'debug',
      language: 'zh_cn',
      accent: 'mandarin',
    })

    voiceInstance.setCallbacks({
      onTextChange: (text) => {
        updateResult(text)
        addLog('info', '识别结果更新', { text })
      },
      onStatusChange: (oldStatus, newStatus) => {
        addLog('info', '状态变更', { from: oldStatus, to: newStatus })
        updateStatus(newStatus, newStatus)
      },
      onError: (error) => {
        addLog('error', '发生错误', { error: error.message })
        updateStatus('error', '错误')
        isRecording.value = false
      },
    })

    addLog('info', '实例创建成功')
    updateStatus('idle', '已创建')
  }
  catch (error) {
    addLog('error', '创建实例失败', { error: error.message })
    alert(`创建实例失败: ${error.message}`)
  }
}

// 开始录音
async function startRecording() {
  if (!voiceInstance) {
    alert('请先创建实例')
    return
  }

  if (isRecording.value) {
    alert('录音已在进行中')
    return
  }

  try {
    await voiceInstance.start()
    isRecording.value = true
    addLog('info', '录音开始')
  }
  catch (error) {
    addLog('error', '开始录音失败', { error: error.message })
    alert(`开始录音失败: ${error.message}`)
  }
}

// 停止录音
function stopRecording() {
  if (!voiceInstance || !isRecording.value) {
    alert('没有正在进行的录音')
    return
  }

  try {
    voiceInstance.stop()
    isRecording.value = false
    addLog('info', '录音停止')
  }
  catch (error) {
    addLog('error', '停止录音失败', { error: error.message })
    alert(`停止录音失败: ${error.message}`)
  }
}

// 销毁实例
function destroyInstance() {
  if (!voiceInstance) {
    alert('没有可销毁的实例')
    return
  }

  try {
    voiceInstance.destroy()
    voiceInstance = null
    isRecording.value = false
    updateStatus('idle', '未初始化')
    updateResult('等待开始录音...')
    addLog('info', '实例已销毁')
  }
  catch (error) {
    addLog('error', '销毁实例失败', { error: error.message })
    alert(`销毁实例失败: ${error.message}`)
  }
}

// 清空日志
function clearLog() {
  logs.value = []
}

// 获取日志样式类
function getLogClass(level) {
  return `log-entry log-${level}`
}

onMounted(() => {
  addLog('info', '页面加载完成，请配置API信息并创建实例')
})

onUnmounted(() => {
  if (voiceInstance) {
    voiceInstance.destroy()
  }
})
</script>

<template>
  <div class="voice-test-wrapper">
    <div class="container">
      <div class="header">
        <h1>🎤 讯飞语音听写测试</h1>
        <p>测试多实例隔离调用和日志记录功能</p>
      </div>

      <div class="config">
        <h3>配置信息</h3>
        <label>APPID:</label>
        <input v-model="appid" type="text" placeholder="请输入讯飞APPID">
        <label>API Secret:</label>
        <input v-model="apisecret" type="password" placeholder="请输入讯飞API Secret">
        <label>API Key:</label>
        <input v-model="apikey" type="password" placeholder="请输入讯飞API Key">
      </div>

      <div class="controls">
        <button class="btn-primary" @click="createInstance">
          创建实例
        </button>
        <button class="btn-success" @click="startRecording">
          开始录音
        </button>
        <button class="btn-danger" @click="stopRecording">
          停止录音
        </button>
        <button class="btn-secondary" @click="destroyInstance">
          销毁实例
        </button>
        <button class="btn-secondary" @click="clearLog">
          清空日志
        </button>
      </div>

      <div class="status" :class="[statusClass]">
        状态: {{ status }}
      </div>

      <div class="result">
        <h3>识别结果:</h3>
        <div>{{ result }}</div>
      </div>

      <div class="log">
        <h3>日志输出:</h3>
        <div class="log-content">
          <div v-for="(log, index) in logs" :key="index" :class="getLogClass(log.level)">
            {{ log.text }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.voice-test-wrapper {
  width: 100%;
  height: 100%;
  padding: 20px;
  background-color: #f5f5f5;
  box-sizing: border-box;
  overflow-y: auto;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header {
  text-align: center;
  margin-bottom: 30px;

  h1 {
    margin: 0 0 10px 0;
    color: #333;
  }

  p {
    margin: 0;
    color: #666;
  }
}

.config {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 5px;
  padding: 15px;
  margin-bottom: 20px;

  h3 {
    margin: 0 0 15px 0;
    color: #333;
  }

  label {
    display: block;
    margin-top: 10px;
    font-weight: bold;
    color: #495057;
  }

  input {
    width: 100%;
    padding: 8px;
    margin: 5px 0;
    border: 1px solid #ced4da;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 14px;

    &:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }
  }
}

.controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;

  button {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
    min-width: 100px;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    &:active {
      transform: translateY(0);
    }
  }

  .btn-primary {
    background-color: #007bff;
    color: white;

    &:hover {
      background-color: #0056b3;
    }
  }

  .btn-success {
    background-color: #28a745;
    color: white;

    &:hover {
      background-color: #1e7e34;
    }
  }

  .btn-danger {
    background-color: #dc3545;
    color: white;

    &:hover {
      background-color: #c82333;
    }
  }

  .btn-secondary {
    background-color: #6c757d;
    color: white;

    &:hover {
      background-color: #545b62;
    }
  }
}

.status {
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
  font-weight: bold;
  text-align: center;

  &.idle {
    background-color: #e9ecef;
    color: #495057;
  }

  &.recording {
    background-color: #d4edda;
    color: #155724;
  }

  &.error {
    background-color: #f8d7da;
    color: #721c24;
  }

  &.initializing {
    background-color: #fff3cd;
    color: #856404;
  }

  &.stopped {
    background-color: #d1ecf1;
    color: #0c5460;
  }
}

.result {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 5px;
  padding: 15px;
  min-height: 100px;
  margin-bottom: 20px;

  h3 {
    margin: 0 0 10px 0;
    color: #333;
  }

  div {
    color: #495057;
    line-height: 1.5;
    word-wrap: break-word;
  }
}

.log {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 5px;
  padding: 15px;

  h3 {
    margin: 0 0 10px 0;
    color: #333;
  }

  .log-content {
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    padding: 10px;

    .log-entry {
      margin-bottom: 5px;
      padding: 2px 0;
      line-height: 1.4;

      &.log-debug {
        color: #6c757d;
      }

      &.log-info {
        color: #007bff;
      }

      &.log-warn {
        color: #ffc107;
      }

      &.log-error {
        color: #dc3545;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .voice-test-wrapper {
    padding: 10px;
  }

  .container {
    padding: 20px;
  }

  .controls {
    flex-direction: column;

    button {
      width: 100%;
    }
  }

  .config input {
    font-size: 16px; // 防止iOS缩放
  }
}
</style>
