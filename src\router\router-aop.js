import { whiteList } from '@/config/constant.js'
import { getToken } from '@/utils/auth.js'
import { progressDone, progressStart } from '@/utils/progress.js'

import router from './index'

router.beforeEach((to, from, next) => {
  progressStart()

  // 设置页面标题
  const title = import.meta.env.VITE_APP_TITLE || 'OEE System'
  document.title = to.meta.title ? `${to.meta.title} - ${title}` : title

  // 获取token
  const token = getToken()

  // 如果有token
  if (token) {
    // 已登录用户可以访问任何页面（包括登录页）
    next()
  } else {
    // 没有token，检查是否在白名单中
    if (whiteList.includes(to.path)) {
      // 在白名单中，直接放行
      next()
    } else {
      // 不在白名单中，重定向到登录页
      next('/login')
    }
  }
})

router.afterEach(() => {
  progressDone()
})
