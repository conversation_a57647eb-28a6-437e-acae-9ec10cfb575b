<script setup>
import { Graph, NodeEvent } from '@antv/g6'
import { onMounted, ref } from 'vue'

import ScannerComponent from '@/components/ScannerComponent/index.vue'

let g6Graph = null
const nodes = ref([])
const edges = ref([])
const activeNode = ref('1')
const cacheNodes = ref([])

const value = ref('')
const focusInput = ref(false)

// 点击右侧item事件
function handleClickNodeListEvent(data) {
  console.log('🚀 ~ handleClickNodeListEvent ~ data:', data)
  activeNode.value = data.id
}

// 失去焦点事件
function handleBlurEvent() {
  focusInput.value = false
}

// 获得焦点事件
function handleFocusEvent() {
  focusInput.value = true
}

// g6渲染事件
function g6Rendering() {
  if (g6Graph) {
    g6Graph?.off()
    g6Graph?.destroy()
  }

  g6Graph = new Graph({
    container: document.getElementById('container'),
    data: { nodes: nodes.value, edges: edges.value },
    node: {
      style: {
        fill: d => d.fill || '#5B6D68', // 填充颜色
        stroke: d => d.stroke || d.fill || '#5B6D68', // 边框颜色
        lineWidth: d => 2, // 线宽
        opacity: d => d.opacity || 0.8, // 不透明度
        labelText: d => d.label || d.id,
        labelFill: '#000',
        size: d => d.size || 20,
      },
    },
    edge: {
      style: {
        stroke: d => d.style?.stroke || '#999',
        lineWidth: d => d.style?.lineWidth || 1,
      },
    },
    layout: {
      type: 'd3-force',
      link: { distance: 80, strength: 0.45 },
      manyBody: { strength: -120 },
    },
    behaviors: ['drag-element-force'],
  })

  g6Graph.on(NodeEvent.CLICK, handleLoadSubNodeEvent)
  g6Graph?.render()
}

// 渲染下级节点数据
function handleLoadSubNodeEvent(data) {
  console.log('🚀 ~ handleLoadSubNodeEvent ~ data:', data)
  g6Rendering()
}
// 渲染事件
function handleRenderingEvent() {
  console.log('home rendering', value.value)
  g6Rendering()
}

// 开启相机事件
const scannerOpen = ref(false)

// 开启相机事件
function handleScannerOpenEvent() {
  scannerOpen.value = true
}

// 回填扫码数据
function handleScannerDataEvent(code) {
  value.value = code
  handleRenderingEvent()
}

onMounted(() => {
  edges.value = [
    { source: '0', target: '1' },
    { source: '0', target: '2' },
    { source: '0', target: '3' },
    { source: '0', target: '4' },
    { source: '0', target: '5' },
    { source: '0', target: '7' },
    { source: '0', target: '8' },
    { source: '0', target: '9' },
    { source: '0', target: '6' },
  ]
  nodes.value = Array.from({ length: 10 })
    .fill(0)
    .map((_, i) => ({ id: `${i}`, label: `无敌无敌无敌无敌无敌无敌无敌无敌无敌无敌${i}` }))

  handleRenderingEvent()
})
</script>

<template>
  <div class="knowledge-graph-wrapper">
    <div class="main-content">
      <div class="option-box">
        <div class="input-content">
          <el-input
            v-model="value"
            size="large"
            :autofocus="true"
            placeholder="输入资产或设备编号"
            @blur="handleBlurEvent"
            @focus="handleFocusEvent"
          />
          <div class="btn search" @click="handleRenderingEvent">
            <img src="@/assets/svg/icon-search.svg" alt="search">
          </div>
        </div>
        <div class="btn scanner" @click="handleScannerOpenEvent">
          <img src="@/assets/svg/icon-scanner.svg" alt="scanner">
        </div>
      </div>
      <div id="container" />
    </div>
    <div class="node-content">
      <div class="node-back">
        <img src="@/assets/svg/icon-back.svg" alt="back">
        <span>返回上一步</span>
      </div>

      <div class="node-arrays global-scrollbar">
        <div
          v-for="item in nodes"
          :key="item.id"
          :class="{ active: item.id === activeNode }"
          class="node-item"
          @click="handleClickNodeListEvent(item)"
        >
          <div class="icon" />
          <div class="attr">
            {{ item.label }}
          </div>
        </div>
      </div>
    </div>

    <ScannerComponent v-model:visible="scannerOpen" @code="handleScannerDataEvent" />
  </div>
</template>

<style lang="less" scoped>
.knowledge-graph-wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  position: relative;

  .main-content {
    flex: 1;
    overflow: hidden;

    .option-box {
      position: absolute;
      top: 20px;
      left: 80px;
      z-index: 1;
      display: flex;

      .input-content {
        width: 240px;
        border-radius: 25px;
        padding: 0 20px;
        background-color: #fff;
        display: flex;

        .search {
          width: 25px;
          height: 100%;
          margin-left: 10px;
        }
      }

      .scanner {
        margin-left: 10px;
        width: 38px;
        height: 38px;
        background-color: #fff;
        border-radius: 25px;
        border: 1px solid #eaeaea;
        box-shadow: 0px 0px 20px 0px #0000000d;

        img {
          transform: scale(0.45);
        }
      }

      .btn {
        cursor: pointer;

        img {
          width: 100%;
          height: 100%;
          opacity: 0.65;
          transition: opacity 0.3s ease-in;
        }

        &:hover {
          img {
            opacity: 1;
          }
        }
      }
    }
  }

  .node-content {
    background-color: #f2f1f0;
    height: 100%;
    width: 20%;
    min-width: 200px;

    .node-back {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      cursor: pointer;
      font-size: 16px;
      color: #808080;
      border-bottom: 1px solid #e5e5e5;
      height: 50px;

      img {
        width: 20px;
        height: 20px;
        margin-right: 10px;
        margin-left: 15px;
      }
    }

    .node-arrays {
      height: calc(100% - 51px);

      .node-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 10px 15px;
        cursor: pointer;

        &.active {
          background-color: #eae1d4 !important;
        }

        &:hover {
          background-color: #eae1d472;
        }

        .icon {
          width: 35px;
          height: 35px;
          background-color: #fff;
          border-radius: 25px;
          margin-right: 10px;
        }

        .attr {
          flex: 1;
          font-size: 14px;
          color: #333;
          line-height: 1.5;
        }
      }
    }
  }
}
</style>

<style lang="less">
.knowledge-graph-wrapper {
  .el-input__wrapper {
    box-shadow: none;
    border: none;
    padding-left: 0;
    padding-right: 0;
  }
}
</style>
