<script setup>
import { onMounted } from 'vue'

import { init } from '@/config/init'

onMounted(() => init())
</script>

<template>
  <router-view />
</template>

<style lang="less">
@import '@/styles/main.css';
@import '@/styles/deformation.less';

#app {
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  font-family: 'Inter', Avenir, Helvetica, Arial, sans-serif;
  font-weight: normal;
}
</style>
