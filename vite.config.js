import path from 'node:path'
import { defineConfig, loadEnv } from 'vite'

import createVitePlugins from './plugins/index.js'

// https://vite.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd())
  const { VITE_APP_ENV } = env
  return {
    base: VITE_APP_ENV === 'production' ? '/' : '/',
    plugins: createVitePlugins(env, command === 'build'),
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        '~': path.resolve(__dirname, './'),
        // 设置别名
        '@': path.resolve(__dirname, './src'),
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
    },
    // CSS预处理器配置
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
          additionalData: `@import "${path.resolve(__dirname, './src/styles/variables.less')}";`,
        },
      },
    },
    // vite 相关配置
    server: {
      port: 81,
      host: true,
      open: false,
      fs: {
        // 允许访问src目录下的文件
        allow: ['..'],
      },
      proxy: {
        '/esg-ims-vue': {
          target: 'http://locahost:18667',
              changeOrigin: true,
              // rewrite: p => p.replace(/^\/agent-api/, ''),
        },
        // https://cn.vitejs.dev/config/#server-proxy
        '/agent-api': {
          target: 'http://***********:9310',
          changeOrigin: true,
          rewrite: p => p.replace(/^\/agent-api/, ''),
        },
        '/agent-mcp': {
          target: 'http://***********:9981',
          // target: 'http://localhost:9081',
          changeOrigin: true,
          // rewrite: (p) => p.replace(/^\/oauth-api/, ''),
        },
      },
    },
  }
})
