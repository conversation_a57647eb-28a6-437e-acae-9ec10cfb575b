{"compilerOptions": {"target": "ES2020", "jsx": "preserve", "lib": ["ES2020", "DOM", "DOM.Iterable"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "bundler", "paths": {"@/*": ["src/*"]}, "resolveJsonModule": true, "allowImportingTsExtensions": true, "allowJs": true, "noEmit": true, "esModuleInterop": true, "isolatedModules": true, "skipLibCheck": true}, "include": ["src/**/*.js", "src/**/*.vue"]}