{"name": "oee-project", "type": "module", "version": "1.0.0", "private": true, "description": "OEE 项目", "scripts": {"dev": "vite", "build": "vite build", "build:development": "vite build --mode development", "build:pwa": "vite build && echo 'PWA构建完成！'", "preview": "vite preview", "preview:pwa": "vite preview --port 4173", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@antv/g6": "^5.0.49", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.9", "crypto-js": "^4.2.0", "element-plus": "^2.9.4", "js-cookie": "^3.0.5", "nprogress": "^0.2.0", "pinia": "^2.3.0", "pinia-plugin-persist": "^1.0.0", "quill": "^2.0.3", "splitpanes": "^3.1.8", "vue": "^3.5.13", "vue-clipboard3": "^2.0.0", "vue-i18n": "^11.0.1", "vue-qrcode-reader": "^5.7.3", "vue-router": "^4.5.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@antfu/eslint-config": "^5.2.2", "@vitejs/plugin-vue": "^4.5.2", "eslint": "^9.34.0", "eslint-plugin-perfectionist": "^4.15.0", "less": "^4.4.1", "less-loader": "^12.3.0", "stylelint": "^16.23.1", "stylelint-order": "^7.0.0", "unplugin-auto-import": "^0.19.0", "unplugin-vue-components": "^0.28.0", "unplugin-vue-setup-extend-plus": "^1.0.1", "vite": "^4.5.2", "vite-plugin-compression": "^0.5.1", "vite-plugin-pwa": "^1.0.3", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-wasm": "^3.5.0"}}