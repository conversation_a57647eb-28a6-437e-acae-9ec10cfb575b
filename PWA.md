# Stella Agent PWA 功能说明

## 概述

本项目已成功集成了PWA（渐进式Web应用）功能，用户可以将应用安装到设备主屏幕，享受原生应用般的体验。

## 主要特性

### 1. 离线支持

- Service Worker 自动缓存应用资源
- 支持离线访问已缓存的内容
- 智能的资源更新策略

### 2. 安装提示

- 自动检测设备是否支持PWA安装
- 友好的安装提示界面
- 支持添加到主屏幕

### 3. 自动更新

- 检测应用新版本
- 用户友好的更新提示
- 一键刷新获取最新版本

### 4. 响应式设计

- 适配各种屏幕尺寸
- 支持横屏和竖屏模式
- 触摸友好的交互设计

## 技术实现

### 使用的插件

- `vite-plugin-pwa`: Vite PWA插件
- `workbox`: Service Worker生成工具

### 核心文件

- `plugins/index.js`: PWA插件配置
- `src/pwa.js`: PWA功能实现
- `public/pwa-icon.svg`: PWA应用图标
- `public/browserconfig.xml`: Windows磁贴配置

### 配置说明

#### PWA Manifest

```javascript
{
  name: 'Stella Agent',
  short_name: 'Stella',
  description: 'Stella Agent 智能代理系统',
  theme_color: '#4F46E5',
  background_color: '#ffffff',
  display: 'standalone',
  orientation: 'portrait',
  scope: '/',
  start_url: '/'
}
```

#### Service Worker策略

- 使用 `CacheFirst` 策略缓存静态资源
- 支持字体文件的智能缓存
- 自动跳过等待和声明客户端

## 使用方法

### 开发环境

1. 启动开发服务器：`npm run dev`
2. PWA功能在开发环境中已启用
3. 可以在浏览器开发者工具中查看Service Worker状态

### 生产环境

1. 构建应用：`npm run build`
2. 部署到支持HTTPS的服务器
3. 用户访问时会自动提示安装

### 安装应用

1. 在支持PWA的浏览器中访问应用
2. 点击地址栏的安装图标
3. 或等待安装提示出现
4. 选择"安装"将应用添加到主屏幕

## 浏览器兼容性

- Chrome 67+
- Edge 79+
- Firefox 67+
- Safari 11.1+
- 移动端浏览器（iOS Safari 11.1+, Chrome Mobile 67+）

## 注意事项

1. **HTTPS要求**: PWA功能需要HTTPS环境
2. **图标格式**: 推荐使用SVG格式的图标以获得最佳效果
3. **缓存策略**: 可根据实际需求调整缓存策略
4. **更新机制**: 应用更新时会自动提示用户刷新

## 故障排除

### Service Worker未注册

- 检查浏览器是否支持Service Worker
- 确认应用运行在HTTPS环境
- 查看浏览器控制台错误信息

### 安装提示未出现

- 确认应用满足PWA安装条件
- 检查manifest.json是否正确生成
- 验证图标文件是否可访问

### 缓存问题

- 清除浏览器缓存
- 在开发者工具中手动更新Service Worker
- 检查workbox配置是否正确

## 更新日志

- v1.0.0: 初始PWA集成
    - 基础Service Worker功能
    - 应用安装提示
    - 自动更新检测
    - 响应式图标设计
