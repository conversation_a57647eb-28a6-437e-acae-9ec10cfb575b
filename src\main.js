// ElementPlus组件
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import { createApp } from 'vue'

import SvgIcon from '@/components/SvgIcon'
import elementIcons from '@/components/SvgIcon/svgicon'

import App from './App.vue'
import { initPWA } from './pwa'
import 'element-plus/dist/index.css'

import router from './router'
import store from './store'

// svg图标
import 'virtual:svg-icons-register'

// PWA功能
import './router/router-aop.js'

const app = createApp(App)
app.use(router)
app.use(store)
app.use(elementIcons)
app.component('svg-icon', SvgIcon)

// 使用element-plus 并且设置全局的大小
app.use(ElementPlus, { size: 'default', locale: zhCn })

app.mount('#app')

// 初始化PWA功能
initPWA()
