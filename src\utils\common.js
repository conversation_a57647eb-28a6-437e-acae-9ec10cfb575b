/**
 * 设置主题样式设置
 * @param themeColor 主题色
 */
export function handleThemeStyle(themeColor) {
  // rgb颜色转Hex颜色
  function rgbToHex(r, g, b) {
    const hex = [r.toString(16), g.toString(16), b.toString(16)]
    for (let i = 0; i < 3; i++) {
      if (hex[i].length === 1) {
        hex[i] = `0${hex[i]}`
      }
    }
    return `#${hex.join('')}`
  }

  // hex颜色转rgb颜色
  function hexToRgb(hex) {
    hex = hex.replace('#', '')

    const value = hex.match(/../g)
    for (let i = 0; i < 3; i++) {
      value[i] = Number.parseInt(value[i], 16)
    }
    return value
  }

  // 变深颜色值
  function handleDarkColor(color, level) {
    const rgb = hexToRgb(color)
    for (let i = 0; i < 3; i++) {
      rgb[i] = Math.floor(rgb[i] * (1 - level))
    }
    return rgbToHex(rgb[0], rgb[1], rgb[2])
  }

  // 变浅颜色值
  function handleLightColor(color, level) {
    const rgb = hexToRgb(color)
    for (let i = 0; i < 3; i++) {
      rgb[i] = Math.floor((255 - rgb[i]) * level + rgb[i])
    }
    return rgbToHex(rgb[0], rgb[1], rgb[2])
  }

  document.documentElement.style.setProperty('--el-color-primary', themeColor)
  for (let i = 1; i <= 9; i++) {
    document.documentElement.style.setProperty(
      `--el-color-primary-light-${i}`,
      `${handleLightColor(themeColor, i / 10)}`,
    )
  }
  for (let i = 1; i <= 9; i++) {
    document.documentElement.style.setProperty(
      `--el-color-primary-dark-${i}`,
      `${handleDarkColor(themeColor, i / 10)}`,
    )
  }
}

/**
 * 获取随机id
 * @param {number} length id长度
 * @param {string} prefix 前缀
 * @returns {string} 随机id
 */
export function nanoId(length = 21, prefix = '') {
  if (length <= 0)
    return ''

  // 检查前缀长度是否超过总长度
  if (prefix.length >= length) {
    console.warn('前缀长度不能大于或等于总长度，返回前缀本身')
    return prefix
  }

  // 计算实际需要生成的随机字符长度
  const actualLength = length - prefix.length

  const chars = '_ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''

  for (let i = 0; i < actualLength; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }

  return prefix + result
}

/**
 * 检查字符串是否为空
 * @param {*} value 要检查的值
 * @returns {boolean} 如果为空返回true，否则返回false
 */
export function isEmptyWithString(value) {
  // 检查是否为null或undefined
  if (value === null || value === undefined) {
    return true
  }

  // 检查是否为字符串类型
  if (typeof value !== 'string') {
    return true
  }

  // 检查是否为空字符串或只包含空白字符
  return value.trim().length === 0
}
