<script setup>
import { storeSystem } from '@/store'

const _storeSystem = storeSystem()

// 切换侧边栏展开和收起状态
function handleTiggleSidebarEvent() {
  _storeSystem.sidebarFold = !_storeSystem.sidebarFold
}
</script>

<template>
  <div class="app-main-wrapper">
    <div class="sidebar-open" @click="handleTiggleSidebarEvent">
      <img src="@/assets/svg/icon-arrow.svg" alt="logo">
    </div>

    <router-view v-slot="{ Component, route }">
      <transition name="fade-transform" mode="out-in">
        <component :is="Component" :key="route.fullPath" />
      </transition>
    </router-view>
  </div>
</template>

<style lang="less" scoped>
.app-main-wrapper {
  height: calc(100vh - @navbar-height);
  background-color: #f8f8f8;

  .sidebar-open {
    position: absolute;
    transform: rotate(180deg);
    width: 60px;
    height: 60px;
    background-color: #dedede;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition:
      background-color 0.2s ease-in-out,
      opacity 0.2s ease-in-out;
    user-select: none;
    display: none;
    opacity: 0.55;

    &:hover {
      opacity: 1;
      background-color: #cac3b8;
    }

    img {
      width: 25px;
      height: 25px;
      object-fit: contain;
    }
  }
}
</style>
