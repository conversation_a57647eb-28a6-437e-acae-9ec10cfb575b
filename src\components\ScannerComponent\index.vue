<script setup>
import { ElMessageBox } from 'element-plus'
import { onMounted, ref, watch } from 'vue'
import { QrcodeStream, setZXingModuleOverrides } from 'vue-qrcode-reader'

import zxingReaderWasmUrl from './zxing_reader.wasm?url'

const props = defineProps({
  // 显示状态
  visible: {
    type: Boolean,
    required: false,
  },
})

const emit = defineEmits(['update:visible', 'code'])

const barcodeFormats = ref([
  'aztec',
  'code_128',
  'code_39',
  'code_93',
  'codabar',
  'databar',
  'databar_expanded',
  'data_matrix',
  'dx_film_edge',
  'ean_13',
  'ean_8',
  'itf',
  'maxi_code',
  'micro_qr_code',
  'pdf417',
  'qr_code',
  'rm_qr_code',
  'upc_a',
  'upc_e',
  'linear_codes',
  'matrix_codes',
])

const error = ref(false)
const message = ref('正在初始化相机，请稍后...')
const loading = ref(true)
const lightOpen = ref(false)
const lightNoSupport = ref(false)
const visible = ref(props.visible)
watch(
  () => props.visible,
  data => (visible.value = data),
)
watch(
  () => visible.value,
  data => emit('update:visible', data),
)

// 处理异常事件
function handleOnErrorEvent(event) {
  error.value = true
  const { name } = event
  message.value = '扫码出现异常，请刷新后重试。'

  if (name === 'NotAllowedError')
    message.value = '您需要授予相机访问权限。'
  if (name === 'NotFoundError')
    message.value = '此设备上没有摄像头。'
  if (name === 'NotSupportedError')
    message.value = '网络连接不安全，必须使用（Https，localhost）。'
  if (name === 'NotReadableError')
    message.value = '相机可能被占用，请检查后重试。'
  if (name === 'OverconstrainedError')
    message.value = '相机不支持扫码组件，请更换设备后重试。'
  if (name === 'StreamApiNotSupportedError')
    message.value = '此浏览器不支持 Stream API，请升级浏览器后重试。'
  if (name === 'InsecureContextError')
    message.value = '仅在安全环境下才允许访问摄像头。请使用 HTTPS 或 localhost，而非 HTTP。'

  console.error('错误信息：', message.value, event)
  ElMessageBox.alert(message.value, '扫码异常', {
    confirmButtonText: '确定',
    type: 'error',
  })
}

// 关闭当前窗口
function handleCloseEvent() {
  visible.value = false
  console.log('handleCloseEvent')
}

// 初始化相机
function handleCameraOnEvent(capabilities) {
  loading.value = false
  lightNoSupport.value = !capabilities.torch
  console.log('相机能力:', capabilities)
}

// 切换灯光开启状态
function handleSwitchLightEvent() {
  lightOpen.value = !lightOpen.value
  console.log('闪光灯状态:', lightOpen.value)
}

// 绘制边框
function handleDrawBoundingBox(arrays, ctx) {
  for (const item of arrays) {
    const {
      boundingBox: { x, y, width, height },
    } = item

    ctx.lineWidth = 2
    ctx.strokeStyle = '#ff0000'
    ctx.strokeRect(x, y, width, height)
  }
}

// 处理数据
function handleDetectDataEvent(codes) {
  const codeArrays = codes.map(code => code.rawValue)
  console.log('🚀 ~ handleDetectDataEvent ~ codeArrays:', codeArrays)
  if (codeArrays.length <= 0)
    return

  setTimeout(() => {
    handleCloseEvent()
    emit('code', codeArrays[0])
  }, 500)
}

onMounted(() => {
  setZXingModuleOverrides({
    locateFile: () => zxingReaderWasmUrl,
  })
})
</script>

<template>
  <div class="scanner-index-wrapper">
    <div v-if="visible" class="content">
      <div v-if="loading" class="loading">
        <div>{{ message }}</div>
        <el-button class="btn" plain @click="handleCloseEvent">
          关闭
        </el-button>
      </div>
      <div v-if="!error" class="option-wrapper">
        <el-button v-if="!lightNoSupport" plain @click="handleSwitchLightEvent">
          {{ lightOpen ? '关闭灯光' : '开启灯光' }}
        </el-button>
        <el-button plain @click="handleCloseEvent">
          关闭窗口
        </el-button>
      </div>

      <QrcodeStream
        v-memo="[lightOpen]"
        :torch="lightOpen"
        :formats="barcodeFormats"
        :track="handleDrawBoundingBox"
        @camera-on="handleCameraOnEvent"
        @detect="handleDetectDataEvent"
        @error="handleOnErrorEvent"
      />
    </div>
  </div>
</template>

<style lang="less" scoped>
.scanner-index-wrapper {
  .option-wrapper {
    position: fixed;
    left: 20px;
    top: 20px;
    z-index: 1;
  }

  .content {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
    background-color: #eee;
  }

  .loading {
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 101;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;

    .btn {
      margin-top: 10px;
    }
  }
}
</style>
