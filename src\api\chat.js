import request from '@/utils/request'

// 聊天消息接口 API返回
export function chat_completions(param) {
  return request({
    url: '/agent-api/api/v1/langgraph/mcp/chat',
    method: 'POST',
    data: param,
  })
}

// 聊天消息接口 流返回
export function chat_completions_streams(param, onMessage, onComplete, onError) {
  const url = '/agent-api/api/v1/langgraph/mcp/chat'

  return fetch(url, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(param),
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()

      function readStream() {
        return reader.read().then(({ done, value }) => {
          if (done) {
            onComplete && onComplete()
            return
          }

          const chunk = decoder.decode(value, { stream: true })
          const lines = chunk.split('\n')

          lines.forEach((line) => {
            if (line.startsWith('data: ')) {
              const dataStr = line.slice(6) // 移除 'data: ' 前缀

              if (dataStr === '[DONE]') {
                onComplete && onComplete()
                return
              }

              try {
                const data = JSON.parse(dataStr)
                onMessage && onMessage(data)
              }
              catch (e) {
                console.warn('Failed to parse SSE data:', dataStr, e)
              }
            }
          })

          return readStream()
        })
      }

      return readStream()
    })
    .catch((error) => {
      console.error('Stream error:', error)
      onError && onError(error)
      throw error
    })
}
