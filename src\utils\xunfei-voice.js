/**
 * 讯飞语音听写工具类
 * 支持多对象隔离调用，完善的日志记录能力
 * 依赖 crypto-js 4.2.0+
 */

import CryptoJS from 'crypto-js'

class XunfeiVoiceLogger {
  constructor(instanceId = 'default') {
    this.instanceId = instanceId
    this.logLevel = 'info' // debug, info, warn, error
  }

  setLogLevel(level) {
    this.logLevel = level
  }

  formatMessage(level, message, data = null) {
    const timestamp = new Date().toISOString()
    const prefix = `[XunfeiVoice:${this.instanceId}] [${timestamp}] [${level.toUpperCase()}]`
    return { prefix, message, data }
  }

  debug(message, data = null) {
    if (['debug'].includes(this.logLevel)) {
      const log = this.formatMessage('debug', message, data)
      console.debug(log.prefix, log.message, data ? log.data : '')
    }
  }

  info(message, data = null) {
    if (['debug', 'info'].includes(this.logLevel)) {
      const log = this.formatMessage('info', message, data)
      console.info(log.prefix, log.message, data ? log.data : '')
    }
  }

  warn(message, data = null) {
    if (['debug', 'info', 'warn'].includes(this.logLevel)) {
      const log = this.formatMessage('warn', message, data)
      console.warn(log.prefix, log.message, data ? log.data : '')
    }
  }

  error(message, error = null) {
    if (['debug', 'info', 'warn', 'error'].includes(this.logLevel)) {
      const log = this.formatMessage('error', message, error)
      console.error(log.prefix, log.message, error ? log.error : '')
    }
  }
}

class XunfeiVoiceDictation {
  constructor(options = {}) {
    // 生成唯一实例ID
    this.instanceId = options.instanceId || `instance_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // 初始化日志记录器
    this.logger = new XunfeiVoiceLogger(this.instanceId)
    this.logger.setLogLevel(options.logLevel || 'info')

    this.logger.info('初始化语音听写实例', { instanceId: this.instanceId })

    // 服务接口认证信息
    this.APPID = options.APPID || ''
    this.APISecret = options.APISecret || ''
    this.APIKey = options.APIKey || ''

    // WebSocket配置
    this.url = options.url || 'wss://iat-api.xfyun.cn/v2/iat'
    this.host = options.host || 'iat-api.xfyun.cn'

    // 回调函数
    this.onTextChange = options.onTextChange || (() => {
    })
    this.onStatusChange = options.onStatusChange || (() => {
    })
    this.onError = options.onError || (() => {
    })

    // 语音识别参数
    this.language = options.language || 'zh_cn'
    this.accent = options.accent || 'mandarin'
    this.domain = options.domain || 'iat'
    this.vadEos = options.vadEos || 5000
    this.dwa = options.dwa || 'wpgs'

    // 状态管理
    this.status = 'idle' // idle, initializing, recording, processing, error, stopped
    this.isInitialized = false
    this.isRecording = false

    // 音频处理
    this.audioContext = null
    this.mediaStream = null
    this.scriptProcessor = null
    this.mediaSource = null
    this.webWorker = null
    this.webSocket = null

    // 数据存储
    this.audioData = []
    this.resultText = ''
    this.resultTextTemp = ''
    this.handlerInterval = null

    // 错误处理
    this.errorCount = 0
    this.maxRetries = options.maxRetries || 3

    this.logger.info('语音听写实例配置完成', {
      APPID: this.APPID ? '已配置' : '未配置',
      language: this.language,
      accent: this.accent,
    })
  }

  /**
   * 设置日志级别
   * @param {string} level - 日志级别: debug, info, warn, error
   */
  setLogLevel(level) {
    this.logger.setLogLevel(level)
    this.logger.info('设置日志级别', { level })
  }

  /**
   * 验证配置
   */
  validateConfig() {
    if (!this.APPID || !this.APIKey || !this.APISecret) {
      const error = new Error('缺少必要的API配置信息')
      this.logger.error('配置验证失败', error)
      throw error
    }
    return true
  }

  /**
   * 生成WebSocket URL
   */
  async getWebSocketUrl() {
    try {
      this.validateConfig()

      const date = new Date().toGMTString()
      const algorithm = 'hmac-sha256'
      const headers = 'host date request-line'
      const signatureOrigin = `host: ${this.host}\ndate: ${date}\nGET /v2/iat HTTP/1.1`

      const signatureSha = CryptoJS.HmacSHA256(signatureOrigin, this.APISecret)
      const signature = CryptoJS.enc.Base64.stringify(signatureSha)
      const authorizationOrigin = `api_key="${this.APIKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`
      const authorization = btoa(authorizationOrigin)

      const wsUrl = `${this.url}?authorization=${authorization}&date=${date}&host=${this.host}`

      this.logger.debug('生成WebSocket URL成功')
      return wsUrl
    }
    catch (error) {
      this.logger.error('生成WebSocket URL失败', error)
      throw error
    }
  }

  /**
   * 初始化音频处理
   */
  async initAudioContext() {
    try {
      if (!this.audioContext) {
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)()
        this.logger.info('创建音频上下文成功')
      }

      await this.audioContext.resume()
      this.logger.debug('音频上下文已恢复')

      return true
    }
    catch (error) {
      this.logger.error('初始化音频上下文失败', error)
      throw error
    }
  }

  /**
   * 初始化Web Worker
   */
  initWebWorker() {
    try {
      if (typeof Worker === 'undefined') {
        throw new TypeError('浏览器不支持Web Worker')
      }

      this.webWorker = new Worker('js/transcode.worker.js')

      this.webWorker.onmessage = (event) => {
        this.audioData.push(...event.data)
      }

      this.webWorker.onerror = (error) => {
        this.logger.error('Web Worker错误', error)
        this.handleError(error)
      }

      this.logger.info('Web Worker初始化成功')
      return true
    }
    catch (error) {
      this.logger.error('初始化Web Worker失败', error)
      throw error
    }
  }

  /**
   * 获取麦克风权限
   */
  async getMicrophonePermission() {
    try {
      const constraints = { audio: true }

      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        this.mediaStream = await navigator.mediaDevices.getUserMedia(constraints)
      }
      else if (navigator.getUserMedia) {
        this.mediaStream = await new Promise((resolve, reject) => {
          navigator.getUserMedia(constraints, resolve, reject)
        })
      }
      else {
        throw new Error('浏览器不支持getUserMedia API')
      }

      this.logger.info('获取麦克风权限成功')
      return true
    }
    catch (error) {
      this.logger.error('获取麦克风权限失败', error)
      throw error
    }
  }

  /**
   * 设置音频处理管道
   */
  setupAudioPipeline() {
    try {
      // 创建脚本处理器
      this.scriptProcessor = this.audioContext.createScriptProcessor(0, 1, 1)

      this.scriptProcessor.onaudioprocess = (e) => {
        if (this.status === 'recording' && this.webWorker) {
          try {
            this.webWorker.postMessage(e.inputBuffer.getChannelData(0))
          }
          catch (error) {
            this.logger.error('发送音频数据到Web Worker失败', error)
          }
        }
      }

      // 创建媒体源
      this.mediaSource = this.audioContext.createMediaStreamSource(this.mediaStream)
      this.mediaSource.connect(this.scriptProcessor)
      this.scriptProcessor.connect(this.audioContext.destination)

      this.logger.info('音频处理管道设置成功')
      return true
    }
    catch (error) {
      this.logger.error('设置音频处理管道失败', error)
      throw error
    }
  }

  /**
   * 连接WebSocket
   */
  async connectWebSocket() {
    try {
      const wsUrl = await this.getWebSocketUrl()

      this.webSocket = new WebSocket(wsUrl)

      this.webSocket.onopen = () => {
        this.logger.info('WebSocket连接成功')
        this.setStatus('recording')
        this.startSendingAudio()
      }

      this.webSocket.onmessage = (event) => {
        this.handleWebSocketMessage(event.data)
      }

      this.webSocket.onerror = (error) => {
        this.logger.error('WebSocket连接错误', error)
        this.handleError(error)
      }

      this.webSocket.onclose = (event) => {
        this.logger.info('WebSocket连接关闭', { code: event.code, reason: event.reason })
        this.cleanup()
      }

      return true
    }
    catch (error) {
      this.logger.error('连接WebSocket失败', error)
      throw error
    }
  }

  /**
   * 开始发送音频数据
   */
  startSendingAudio() {
    // 发送初始帧
    this.sendAudioFrame(0)

    // 定时发送音频数据
    this.handlerInterval = setInterval(() => {
      if (!this.webSocket || this.webSocket.readyState !== WebSocket.OPEN) {
        this.logger.warn('WebSocket未连接，停止发送音频数据')
        this.stopSendingAudio()
        return
      }

      if (this.audioData.length === 0) {
        if (this.status === 'stopped') {
          this.sendAudioFrame(2) // 结束帧
          this.stopSendingAudio()
        }
        return
      }

      this.sendAudioFrame(1) // 中间帧
    }, 40)
  }

  /**
   * 停止发送音频数据
   */
  stopSendingAudio() {
    if (this.handlerInterval) {
      clearInterval(this.handlerInterval)
      this.handlerInterval = null
      this.logger.debug('停止发送音频数据')
    }
  }

  /**
   * 发送音频帧
   * @param {number} status - 0: 开始帧, 1: 中间帧, 2: 结束帧
   */
  sendAudioFrame(status) {
    try {
      const audioData = status === 2 ? [] : this.audioData.splice(0, 1280)

      const frame = {
        data: {
          status,
          format: 'audio/L16;rate=16000',
          encoding: 'raw',
          audio: this.toBase64(audioData),
        },
      }

      if (status === 0) {
        // 开始帧包含业务参数
        frame.common = { app_id: this.APPID }
        frame.business = {
          language: this.language,
          domain: this.domain,
          accent: this.accent,
          vad_eos: this.vadEos,
          dwa: this.dwa,
        }
      }

      this.webSocket.send(JSON.stringify(frame))
      this.logger.debug(`发送音频帧`, { status, audioLength: audioData.length })
    }
    catch (error) {
      this.logger.error('发送音频帧失败', error)
      this.handleError(error)
    }
  }

  /**
   * 处理WebSocket消息
   */
  handleWebSocketMessage(data) {
    try {
      const jsonData = JSON.parse(data)

      if (jsonData.code !== 0) {
        this.logger.error('WebSocket返回错误', jsonData)
        this.handleError(new Error(`WebSocket错误: ${jsonData.message || jsonData.code}`))
        return
      }

      if (jsonData.data && jsonData.data.result) {
        this.processRecognitionResult(jsonData.data.result)
      }

      if (jsonData.data && jsonData.data.status === 2) {
        this.logger.info('语音识别完成')
        this.setStatus('stopped')
      }
    }
    catch (error) {
      this.logger.error('处理WebSocket消息失败', error)
    }
  }

  /**
   * 处理识别结果
   */
  processRecognitionResult(result) {
    try {
      let str = ''
      const { ws } = result

      for (let i = 0; i < ws.length; i++) {
        str = str + ws[i].cw[0].w
      }

      if (result.pgs) {
        if (result.pgs === 'apd') {
          this.resultText = this.resultTextTemp
        }
        this.resultTextTemp = this.resultText + str
        this.onTextChange(this.resultTextTemp)
      }
      else {
        this.resultText += str
        this.onTextChange(this.resultText)
      }

      this.logger.debug('处理识别结果', { text: str, pgs: result.pgs })
    }
    catch (error) {
      this.logger.error('处理识别结果失败', error)
    }
  }

  /**
   * 设置状态
   */
  setStatus(newStatus) {
    if (this.status !== newStatus) {
      const oldStatus = this.status
      this.status = newStatus

      this.logger.info('状态变更', { from: oldStatus, to: newStatus })
      this.onStatusChange(oldStatus, newStatus)
    }
  }

  /**
   * 错误处理
   */
  handleError(error) {
    this.errorCount++
    this.logger.error('发生错误', error)

    if (this.errorCount <= this.maxRetries) {
      this.logger.warn(`尝试重试 (${this.errorCount}/${this.maxRetries})`)
      this.reconnect()
    }
    else {
      this.setStatus('error')
      this.onError(error)
    }
  }

  /**
   * 重连机制
   */
  async reconnect() {
    try {
      this.logger.info('开始重连')
      await this.cleanup()
      await this.init()
      if (this.isRecording) {
        await this.start()
      }
    }
    catch (error) {
      this.logger.error('重连失败', error)
      this.handleError(error)
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.logger.info('开始清理资源')

    // 停止发送音频数据
    this.stopSendingAudio()

    // 关闭WebSocket
    if (this.webSocket) {
      this.webSocket.close()
      this.webSocket = null
    }

    // 停止音频流
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop())
      this.mediaStream = null
    }

    // 断开音频处理管道
    if (this.mediaSource) {
      this.mediaSource.disconnect()
      this.mediaSource = null
    }

    if (this.scriptProcessor) {
      this.scriptProcessor.disconnect()
      this.scriptProcessor = null
    }

    // 关闭Web Worker
    if (this.webWorker) {
      this.webWorker.terminate()
      this.webWorker = null
    }

    // 清空音频数据
    this.audioData = []

    this.logger.info('资源清理完成')
  }

  /**
   * 音频数据转Base64
   */
  toBase64(buffer) {
    let binary = ''
    const bytes = new Uint8Array(buffer)
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return window.btoa(binary)
  }

  /**
   * 初始化
   */
  async init() {
    try {
      this.logger.info('开始初始化')
      this.setStatus('initializing')

      this.validateConfig()
      await this.initAudioContext()
      this.initWebWorker()
      await this.getMicrophonePermission()
      this.setupAudioPipeline()

      this.isInitialized = true
      this.setStatus('idle')
      this.logger.info('初始化完成')

      return true
    }
    catch (error) {
      this.logger.error('初始化失败', error)
      this.setStatus('error')
      throw error
    }
  }

  /**
   * 开始录音
   */
  async start() {
    try {
      this.logger.info('开始录音')

      if (!this.isInitialized) {
        await this.init()
      }

      this.isRecording = true
      this.resultText = ''
      this.resultTextTemp = ''
      this.audioData = []
      this.errorCount = 0

      await this.connectWebSocket()

      this.logger.info('录音开始成功')
      return true
    }
    catch (error) {
      this.logger.error('开始录音失败', error)
      this.handleError(error)
      throw error
    }
  }

  /**
   * 停止录音
   */
  stop() {
    try {
      this.logger.info('停止录音')

      this.isRecording = false
      this.setStatus('stopped')

      // 发送结束帧
      if (this.webSocket && this.webSocket.readyState === WebSocket.OPEN) {
        this.sendAudioFrame(2)
      }

      this.logger.info('录音停止成功')
      return true
    }
    catch (error) {
      this.logger.error('停止录音失败', error)
      throw error
    }
  }

  /**
   * 销毁实例
   */
  destroy() {
    try {
      this.logger.info('销毁实例')

      this.stop()
      this.cleanup()

      // 关闭音频上下文
      if (this.audioContext) {
        this.audioContext.close()
        this.audioContext = null
      }

      this.isInitialized = false
      this.logger.info('实例销毁完成')
    }
    catch (error) {
      this.logger.error('销毁实例失败', error)
    }
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      status: this.status,
      isInitialized: this.isInitialized,
      isRecording: this.isRecording,
      resultText: this.resultText,
      errorCount: this.errorCount,
    }
  }

  /**
   * 设置识别参数
   */
  setParams(params = {}) {
    const { language, accent, domain, vadEos, dwa } = params

    if (language)
      this.language = language
    if (accent)
      this.accent = accent
    if (domain)
      this.domain = domain
    if (vadEos)
      this.vadEos = vadEos
    if (dwa)
      this.dwa = dwa

    this.logger.info('更新识别参数', params)
  }

  /**
   * 设置回调函数
   */
  setCallbacks(callbacks = {}) {
    if (callbacks.onTextChange)
      this.onTextChange = callbacks.onTextChange
    if (callbacks.onStatusChange)
      this.onStatusChange = callbacks.onStatusChange
    if (callbacks.onError)
      this.onError = callbacks.onError

    this.logger.info('更新回调函数', Object.keys(callbacks))
  }
}

// 导出类
export default XunfeiVoiceDictation

// 兼容性导出
if (typeof window !== 'undefined') {
  window.XunfeiVoiceDictation = XunfeiVoiceDictation
}
