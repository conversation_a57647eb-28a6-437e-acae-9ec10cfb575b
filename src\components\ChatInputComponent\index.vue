<script setup>
const props = defineProps({
  value: {
    type: String,
    default: '',
    required: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:value', 'btnSend', 'btnVoice', 'btnCancel'])

const value = ref(props.value)
const focusInput = ref(false)
const containerRef = ref(null)

watch(
  () => props.value,
  data => (value.value = data),
)
watch(
  () => value.value,
  data => emit('update:value', data),
)

// 检查元素是否在容器内
function isElementInContainer(element) {
  return containerRef.value?.contains(element)
}

// 检查是否应该保持焦点状态
function shouldKeepFocus(activeElement) {
  // 如果焦点在容器内，保持焦点状态
  if (isElementInContainer(activeElement)) {
    return true
  }

  // 如果焦点在body上，可能是按钮点击导致的，需要进一步检查
  if (activeElement === document.body) {
    return false
  }

  return false
}

// 失去焦点事件
function handleBlurEvent() {
  setTimeout(() => {
    // 延迟检查焦点状态，避免按钮点击时的误判
    const currentActiveElement = document.activeElement
    if (!shouldKeepFocus(currentActiveElement)) {
      focusInput.value = false
    }
  }, 150)
}

// 获得焦点事件
function handleFocusEvent() {
  focusInput.value = true
}

// 点击按钮事件
function handleBtnClickEvent(type, event) {
  event.stopPropagation()
  focusInput.value = true

  // 确保输入框保持焦点
  setTimeout(() => {
    const inputElement = containerRef.value?.querySelector('.el-textarea__inner')
    if (inputElement && document.activeElement === document.body) {
      inputElement.focus()
    }
  }, 50)

  // 触发对应的事件
  const eventMap = { send: 'btnSend', voice: 'btnVoice', cancel: 'btnCancel' }
  if (eventMap[type])
    emit(eventMap[type])
}

// 点击容器事件
function handleContainerClick() {
  focusInput.value = true
}

// 键盘事件处理
function handleKeydown(event) {
  if (event.key === 'Enter' && !event.shiftKey) {
    // 阻止默认的换行行为
    event.preventDefault()
    emit('btnSend')
  }
}
</script>

<template>
  <div ref="containerRef" class="chat-input-wrapper" :class="{ focus: focusInput }" @click="handleContainerClick">
    <div class="inner">
      <div class="icon chat">
        <img src="@/assets/svg/icon-chat.svg" alt="chat">
      </div>
      <div class="input-chat">
        <el-input
          v-model="value"
          :clearable="true"
          :autofocus="true"
          type="textarea"
          :autosize="{ minRows: 1, maxRows: 2 }"
          resize="none"
          placeholder="请输入您的问题"
          @blur="handleBlurEvent"
          @focus="handleFocusEvent"
          @keydown="handleKeydown"
        />
      </div>
      <div class="icon btn voice" @click="handleBtnClickEvent('voice', $event)">
        <img src="@/assets/svg/icon-voice.svg" alt="voice">
      </div>
      <div
        class="icon btn send"
        :class="{ disabled }"
        @click="!disabled && handleBtnClickEvent('send', $event)"
      >
        <img src="@/assets/svg/icon-send1.svg" alt="send">
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.chat-input-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  box-sizing: border-box;
  transition: width 0.25s ease, opacity 0.25s ease;

  &.focus {
    .inner {
      width: 75%;
      opacity: 1;
    }
  }

  .inner {
    width: 45%;
    height: 60px;
    border-radius: 50px;
    background-color: #fff;
    border: 1px solid #eaeaea;
    box-shadow: 0 0 20px 0 #0000000d;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0.6;
    transition: width 0.25s ease, opacity 0.25s ease;

    &:hover {
      opacity: 1;
    }

    .btn {
      cursor: pointer;

      &.disabled {
        cursor: not-allowed;
        opacity: 0.5;

        &:hover {
          img {
            opacity: 0.5;
          }
        }
      }

      img {
        transition: opacity 0.2s ease-in-out;
        opacity: 0.75;
      }

      &:hover {
        img {
          opacity: 1;
        }
      }
    }

    .icon {
      width: 30px;
      height: 30px;

      &.chat {
        margin-left: 25px;
      }

      &.voice {
        img {
          transform: scale(0.55);
        }
      }

      &.send {
        margin-left: 15px;
        border-radius: 50%;
        background-color: #f2f1f0;
        margin-right: 20px;

        img {
          transform: scale(0.55);
          object-fit: unset;
        }
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .input-chat {
      flex: 1;
    }
  }
}
</style>

<style lang="less">
.input-chat {
  .el-textarea__inner {
    box-shadow: none;
    border: none;
  }
}
</style>
