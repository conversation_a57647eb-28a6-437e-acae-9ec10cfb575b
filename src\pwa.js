// PWA 注册和更新提示
export function registerPWA() {
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
      navigator.serviceWorker
        .register('/sw.js')
        .then((registration) => {
          console.log('SW registered: ', registration)

          // 检查更新
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                // 有新版本可用，显示更新提示
                showUpdateNotification()
              }
            })
          })
        })
        .catch((registrationError) => {
          console.log('SW registration failed: ', registrationError)
        })
    })
  }
}

// 显示更新提示
function showUpdateNotification() {
  // 创建更新提示元素
  const updateNotification = document.createElement('div')
  updateNotification.id = 'pwa-update-notification'
  updateNotification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #4CAF50;
    color: white;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 9999;
    max-width: 300px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  `

  updateNotification.innerHTML = `
    <div style="display: flex; align-items: center; justify-content: space-between;">
      <div>
        <div style="font-weight: 600; margin-bottom: 4px;">有新版本可用</div>
        <div style="font-size: 14px; opacity: 0.9;">点击刷新以获取最新版本</div>
      </div>
      <button id="pwa-update-btn" style="
        background: white;
        color: #4CAF50;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 600;
        margin-left: 12px;
      ">刷新</button>
    </div>
  `

  document.body.appendChild(updateNotification)

  // 绑定刷新按钮事件
  document.getElementById('pwa-update-btn').addEventListener('click', () => {
    window.location.reload()
  })

  // 5秒后自动隐藏
  setTimeout(() => {
    if (updateNotification.parentNode) {
      updateNotification.parentNode.removeChild(updateNotification)
    }
  }, 5000)
}

// 安装提示
export function showInstallPrompt() {
  let deferredPrompt

  window.addEventListener('beforeinstallprompt', (e) => {
    // 阻止默认的安装提示
    e.preventDefault()
    deferredPrompt = e

    // 显示自定义安装提示
    showInstallNotification()
  })
}

// 显示安装提示
function showInstallNotification() {
  const installNotification = document.createElement('div')
  installNotification.id = 'pwa-install-notification'
  installNotification.style.cssText = `
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #2196F3;
    color: white;
    padding: 16px 24px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    display: flex;
    align-items: center;
    gap: 12px;
  `

  installNotification.innerHTML = `
    <div>
      <div style="font-weight: 600; margin-bottom: 4px;">安装应用</div>
      <div style="font-size: 14px; opacity: 0.9;">将 Stella Agent 添加到主屏幕</div>
    </div>
    <button id="pwa-install-btn" style="
      background: white;
      color: #2196F3;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 600;
    ">安装</button>
    <button id="pwa-dismiss-btn" style="
      background: transparent;
      color: white;
      border: 1px solid white;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 600;
    ">稍后</button>
  `

  document.body.appendChild(installNotification)

  // 绑定安装按钮事件
  document.getElementById('pwa-install-btn').addEventListener('click', async () => {
    if (deferredPrompt) {
      deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice
      if (outcome === 'accepted') {
        console.log('用户接受了安装提示')
      }
      deferredPrompt = null
    }
    installNotification.remove()
  })

  // 绑定稍后按钮事件
  document.getElementById('pwa-dismiss-btn').addEventListener('click', () => {
    installNotification.remove()
  })

  // 10秒后自动隐藏
  setTimeout(() => {
    if (installNotification.parentNode) {
      installNotification.parentNode.removeChild(installNotification)
    }
  }, 10000)
}

// 初始化PWA功能
export function initPWA() {
  console.warn('initPWA 事件暂停使用')
  // registerPWA();
  // showInstallPrompt();
}
