<script setup>
import { onMounted } from 'vue'

const props = defineProps({
  // 显示状态
  visible: {
    type: Boolean,
    required: false,
  },
})

const emit = defineEmits(['update:visible'])

const visible = ref(props.visible)

watch(
  () => props.visible,
  data => (visible.value = data),
)
watch(
  () => visible.value,
  data => emit('update:visible', data),
)

// 关闭事件
function handleCloseEvent() {
  visible.value = false
}

// 删除聊天历史
function handleDleteHistoryEvent(data) {
  console.log('🚀 ~ handleDleteHistoryEvent ~ data:', data)
}

// 点击切换聊天记录
function handleClickHistoryItemEvent(data) {
  console.log('🚀 ~ handleClickHistoryItemEvent ~ data:', data)
}

onMounted(() => {
  // console.log('voice-input mounted');
})
</script>

<template>
  <div :class="{ active: visible }" class="chat-history-wrapper">
    <div class="mask" @click="handleCloseEvent" />

    <div class="container">
      <div class="header">
        <div class="title">
          历史记录
        </div>
        <div class="icon" @click="handleCloseEvent">
          <img src="@/assets/svg/icon-close-b.svg" alt="Close">
        </div>
      </div>

      <div class="body">
        <div v-for="item in 20" :key="item" class="history-item" @click="handleClickHistoryItemEvent(item)">
          <div class="content">
            <div class="time">
              2025-09-04 00:00:00
            </div>
            <div class="message">
              <div class="question">
                <span class="label">Q:</span>
                <span class="text">{{ item }}</span>
              </div>
              <div class="answer">
                <span class="label">A:</span>
                <span class="text">{{ item }}</span>
              </div>
            </div>
          </div>
          <div class="delete-btn" @click="handleDleteHistoryEvent(item)">
            <img src="@/assets/svg/icon-delete.svg" alt="Delete">
          </div>
        </div>

        <div class="no-data">
          <img src="@/assets/svg/icon-empty.svg" alt="empty">
          <span class="empty-text">暂无数据</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.chat-history-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 100;
  visibility: hidden;
  opacity: 0;
  transition:
    visibility 0s linear 0.3s,
    opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 5;

  &.active {
    visibility: visible;
    opacity: 1;
    transition:
      visibility 0s linear 0s,
      opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    .mask {
      opacity: 1;
    }

    .container {
      transform: translateX(0);
    }
  }

  .mask {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .container {
    position: absolute;
    top: 0;
    right: 0;
    width: 25%;
    min-width: 280px;
    height: 100vh;
    background: #fff;
    box-shadow: -4px 0 16px rgba(0, 0, 0, 0.1);
    transform: translateX(100%);
    will-change: transform;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 15px 10px 15px;
      border-bottom: 1px solid #eee;

      .title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      .icon {
        width: 26px;
        height: 26px;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(0, 0, 0, 0.05);
        }

        img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .body {
      padding: 0 15px;
      overflow-y: auto;

      .history-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 12px;
        border-radius: 8px;
        background-color: #f8f9fa;
        border: 1px solid #eee;
        transition: all 0.3s ease;
        gap: 12px;
        margin-top: 15px;
        cursor: pointer;

        &:hover {
          background-color: #f5f7fa;
          border-color: #8e6f47;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        &:last-child {
          margin-bottom: 20px;
        }

        .content {
          flex: 1;
          min-width: 0;

          .time {
            font-size: 12px;
            color: #999;
            margin-bottom: 8px;
          }

          .message {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .question,
            .answer {
              display: flex;
              gap: 8px;
              font-size: 14px;
              line-height: 1.5;

              .label {
                color: #8e6f47;
                font-weight: 500;
                flex-shrink: 0;
              }

              .text {
                color: #333;
                flex: 1;
                min-width: 0;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }

            .answer {
              color: #666;
            }
          }
        }

        .delete-btn {
          width: 26px;
          height: 26px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.3s ease;
          flex-shrink: 0;

          &:hover {
            background-color: #8e6f4739;
          }

          img {
            width: 16px;
            height: 16px;
            opacity: 0.6;
          }
        }
      }

      .no-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 0;
        color: #999;

        img {
          width: 48px;
          height: 48px;
          margin-bottom: 12px;
          opacity: 0.5;
        }

        .empty-text {
          font-size: 14px;
        }
      }
    }
  }
}
</style>
