<script setup>
import { ElMessage } from 'element-plus'

import { storeCache } from '@/store/index.js'
import { isEmptyWithString } from '@/utils/common.js'

const _storeCache = storeCache()

const current = ref(1)
const pageSize = ref(10)
const pageTotal = ref(100)
const dataList = ref([])

const inputValue = ref('')
const valueStatus = computed(() => !isEmptyWithString(inputValue.value))
const historyArrays = computed(() => JSON.parse(JSON.stringify(_storeCache.documentSearchHistory)))

// 存储用户搜索历史
function handleStoreHistory(value) {
  let arrays = JSON.parse(JSON.stringify(_storeCache.documentSearchHistory))
  arrays = arrays.reverse()

  // 已经存在则删除记录
  arrays = arrays.filter(item => item !== value)
  arrays = arrays.slice(-4)
  arrays.push(value)
  _storeCache.documentSearchHistory = arrays.reverse()
}

// 点击历史记录
function handleClickHistoryItemEvent(data) {
  inputValue.value = data
  handleSearchEvent(data)
}

// 搜索事件
function handleSearchEvent() {
  if (isEmptyWithString(inputValue.value)) {
    ElMessage({ message: '请输入内容后重试', type: 'warning', plain: true })
    return
  }

  handleStoreHistory(inputValue.value)
}

// 改变分页大小
function handleSizeChangeEvent(value) {
  if (current.value * value > pageTotal.value)
    current.value = 1
  console.log('handleSizeChangeEvent', value)
}

// 改变当前页码
function handleCurrentChangeEvent(value) {
  if (!value)
    current.value = 1
  current.value = value
  console.log('handleCurrentChangeEvent', value)
}

// 预览文档
function handlePreviewDocument(data) {
  console.log('handlePreviewDocument', data)
  window.open(data.url)
}

onMounted(() => {
  // inputValue.value = '1'
  // handleSearchEvent()

  for (let i = 0; i < 10; i++) {
    dataList.value.push({
      index: i + 1,
      name: `文档名称${i + 1}`,
      updateTime: '2023-01-01',
      url: 'https://file-agent.esg-cloud.com/onlinePreview?url=aHR0cHM6Ly9maWxlLWFnZW50LmVzZy1jbG91ZC5jb20vZGVtby%2Fos4fnlKLnrqHnkIZfSU9FLnhsc3g%3D',
    })
  }
})
</script>

<template>
  <div class="document-wrapper">
    <div class="container1">
      <div class="header-box">
        <div v-if="!valueStatus" class="logo">
          <img src="@/assets/svg/icon-stella1.svg" alt="Logo">
        </div>
        <div class="input-box">
          <div class="input">
            <el-input
              v-model="inputValue"
              resize="none"
              :autofocus="true"
              placeholder="请输入文档关键字"
              @keyup.enter="handleSearchEvent"
            />
          </div>
          <div class="btn search" @click="handleSearchEvent">
            <img src="@/assets/svg/icon-search.svg" alt="search">
          </div>
        </div>
        <div v-if="!valueStatus && historyArrays.length > 0" class="history-box">
          <span>近期搜索：</span>
          <span v-for="(item, index) in historyArrays" :key="index" @click="handleClickHistoryItemEvent(item)">
            <span class="content">{{ item }}</span>
            <span class="delimiter">、</span>
          </span>
        </div>
      </div>

      <div v-if="valueStatus" class="document-box">
        <el-empty v-if="dataList.length <= 0" :image-size="150" description="暂无数据" />

        <el-table v-else :data="dataList" stripe>
          <el-table-column width="100px" prop="index" label="项次" />
          <el-table-column :show-overflow-tooltip="true" prop="name" label="资源名称" />
          <el-table-column :show-overflow-tooltip="true" prop="tags" label="相关标签" />
          <el-table-column :show-overflow-tooltip="true" prop="updateTime" label="更新日期" />
          <el-table-column width="80px" prop="option" label="操作">
            <template #default="scope">
              <div class="option-view icon" @click="handlePreviewDocument(scope.row)">
                <img src="@/assets/svg/icon-eye.svg" alt="Option">
              </div>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          :current-page="current"
          :page-sizes="[1, 5, 10, 20, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          @size-change="handleSizeChangeEvent"
          @current-change="handleCurrentChangeEvent"
        />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.document-wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;

  .container1 {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .header-box {
      width: 50%;
      min-width: 360px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .logo {
        width: 180px;
        height: 75px;
        margin-bottom: 50px;
      }

      .input-box {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 65px;
        background-color: #fff;
        border-radius: 50px;
        padding: 0 25px;
        box-sizing: border-box;

        .input {
          flex: 1;

          .el-input {
            width: 100%;
            font-size: 16px;
            padding-right: 15px;
          }
        }

        .search {
          width: 35px;
          height: 35px;
          cursor: pointer;
          opacity: .65;
          transition: opacity .3s ease-in-out;

          &:hover {
            opacity: 1;
          }

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }

      .history-box {
        color: #808080;
        font-size: 14px;
        margin-top: 15px;
        padding-left: 25px;
        width: 100%;
        box-sizing: border-box;

        span {
          .content {
            cursor: pointer;
            text-decoration: underline;
            transition: color .3s;

            &:hover {
              color: #000;
            }
          }

          &:last-child {
            .delimiter {
              display: none;
            }
          }
        }
      }
    }

    .document-box {
      width: 70%;
      min-width: 360px;
      margin-top: 30px;

      .option-view {
        width: 23px;
        height: 23px;
        cursor: pointer;
        opacity: .65;
        transition: opacity .3s ease-in-out;

        &:hover {
          opacity: 1;
        }
      }

      .el-pagination {
        float: right;
        margin-top: 20px;
      }
    }
  }
}
</style>

<style lang="less">
.document-wrapper {
  .el-input__wrapper {
    box-shadow: none;
    border: none;
    padding: 0;
  }
}
</style>
