<script setup>
import { ElMessageBox } from 'element-plus'
import { onMounted, ref, watch } from 'vue'

import XunfeiVoiceDictation from '@/utils/xunfei-voice.js'

const props = defineProps({
  // 显示状态
  visible: {
    type: Boolean,
    required: false,
  },
})

const emit = defineEmits(['update:visible', 'data'])

const visible = ref(props.visible)
const content = ref('等待开始录音...')

const appid = ref('5ec244d5')
const apisecret = ref('37912e3e3f205e2a6201ec290452470a')
const apikey = ref('78b6c006f1f3df5e24d315e3dff09212')
let voiceInstance = null
const status = ref('未初始化')
const statusClass = ref('idle')
const isRecording = ref(false)

watch(
  () => props.visible,
  data => (visible.value = data),
)
watch(
  () => visible.value,
  data => emit('update:visible', data),
)

// 关闭当前窗口
function handleCloseEvent() {
  visible.value = false
  console.log('handleCloseEvent')
}

// 处理音频开始，停止事件
async function handleVoiceStatusEvent(status) {
  console.log('🚀 ~ handleVoiceStatusEvent ~ status:', status)

  // 开始录音
  if (status === 'start') {
    if (voiceInstance !== null) {
      handleStopRecordingEvent()
      handleDestroyInstance()
    }

    handleCreateInstance()
    await handleStartRecording()
  }

  // 停止录音
  if (status === 'stop') {
    handleStopRecordingEvent()
    handleDestroyInstance()
  }
}

// 创建录音实例
function handleCreateInstance() {
  if (!appid.value || !apisecret.value || !apikey.value) {
    console.warn('请填写完整的API配置信息')
    return
  }

  try {
    voiceInstance = new XunfeiVoiceDictation({
      APPID: appid.value,
      APISecret: apisecret.value,
      APIKey: apikey.value,
      instanceId: 'vue_test_instance',
      logLevel: 'debug',
      language: 'zh_cn',
      accent: 'mandarin',
    })

    voiceInstance.setCallbacks({
      onTextChange: (text) => {
        updateResult(text)
        console.info('识别结果更新', { text })
      },
      onStatusChange: (oldStatus, newStatus) => {
        console.info('状态变更', { from: oldStatus, to: newStatus })
        updateStatus(newStatus, newStatus)
      },
      onError: (error) => {
        console.info('发生错误', { error: error.message })
        updateStatus('error', '错误')
        isRecording.value = false
      },
    })

    console.info('实例创建成功')
    updateStatus('idle', '已创建')
  }
  catch (error) {
    console.error('创建实例失败', { error: error.message })
    handleMessageBox(`创建实例失败: ${error.message}`, null, 'error')
  }
}

// 开始录音事件
async function handleStartRecording() {
  if (voiceInstance === null) {
    console.warn('请先创建实例')
    handleMessageBox('请先创建实例', null, 'error')
    return
  }

  if (isRecording.value) {
    console.warn('录音已在进行中')
    handleMessageBox(`录音已在进行中: ${error.message}`, null, 'warn')
    return
  }

  try {
    await voiceInstance.start()
    isRecording.value = true
    console.info('录音开始')
  }
  catch (error) {
    console.error(`开始录音失败: ${error.message}`)
    handleMessageBox(`开始录音失败: ${error.message}`, null, 'error')
  }
}

// 停止录音事件
function handleStopRecordingEvent() {
  if (!voiceInstance || !isRecording.value) {
    console.warn('没有正在进行的录音')
    handleMessageBox(`没有正在进行的录音: ${error.message}`, null, 'warn')
    return
  }

  try {
    voiceInstance.stop()
    isRecording.value = false
  }
  catch (error) {
    console.error(`停止录音失败: ${error.message}`)
    handleMessageBox(`停止录音失败: ${error.message}`, null, 'error')
  }
}

// 销毁实例对象
function handleDestroyInstance() {
  if (!voiceInstance) {
    console.warn('没有可销毁的实例')
    return
  }

  try {
    voiceInstance.destroy()
    voiceInstance = null
    isRecording.value = false
    updateStatus('idle', '未初始化')
    updateResult('等待开始录音...')
  }
  catch (error) {
    console.error(`销毁实例失败: ${error.message}`)
    handleMessageBox(`销毁实例失败: ${error.message}`, null, 'error')
  }
}

// 更新状态显示
function updateStatus(newStatus, text) {
  status.value = text
  statusClass.value = newStatus
}

// 更新识别结果
function updateResult(text) {
  content.value = text || '等待识别结果...'
}

// 展示弹窗信息
function handleMessageBox(message, title, type) {
  if (!type)
    type = 'info'
  if (!title)
    title = '提示信息'
  if (!message)
    message = '未知错误，请稍联系管理员。'

  ElMessageBox.alert(message, title, {
    confirmButtonText: '确定',
    type,
  })
}

onMounted(() => {
  console.log('voice-input mounted')
})
</script>
