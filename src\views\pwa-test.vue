<script setup>
import { onMounted, onUnmounted, ref } from 'vue'

import ScannerComponent from '@/components/ScannerComponent/index.vue'

// 响应式数据
const swStatus = ref('unknown')
const swStatusText = ref('检查中...')
const swRegistrationTime = ref('')
const canInstall = ref(false)
const hasUpdate = ref(false)
const networkStatus = ref('online')

// 安装提示相关
let deferredPrompt = null

// 检查Service Worker状态
async function checkSWStatus() {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.getRegistration()
      if (registration) {
        swStatus.value = 'registered'
        swStatusText.value = '已注册'
        swRegistrationTime.value = new Date(
          registration.installing?.state === 'installed'
            ? Date.now()
            : registration.installing?.state === 'installing'
              ? Date.now() - 10000
              : Date.now(),
        ).toLocaleString()

        // 监听更新
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              hasUpdate.value = true
            }
          })
        })
      }
      else {
        swStatus.value = 'not-registered'
        swStatusText.value = '未注册'
      }
    }
    catch (error) {
      swStatus.value = 'error'
      swStatusText.value = `错误: ${error.message}`
    }
  }
  else {
    swStatus.value = 'not-supported'
    swStatusText.value = '不支持'
  }
}

// 安装应用
async function installApp() {
  if (deferredPrompt) {
    deferredPrompt.prompt()
    const { outcome } = await deferredPrompt.userChoice
    if (outcome === 'accepted') {
      console.log('用户接受了安装提示')
      canInstall.value = false
    }
    deferredPrompt = null
  }
}

// 更新应用
function updateApp() {
  window.location.reload()
}

// 检查网络状态
function checkNetworkStatus() {
  networkStatus.value = navigator.onLine ? 'online' : 'offline'
}

// 监听安装提示
function handleBeforeInstallPrompt(e) {
  e.preventDefault()
  deferredPrompt = e
  canInstall.value = true
}

// 监听网络状态变化
function handleOnline() {
  networkStatus.value = 'online'
}

function handleOffline() {
  networkStatus.value = 'offline'
}

onMounted(() => {
  // 检查Service Worker状态
  checkSWStatus()

  // 检查网络状态
  checkNetworkStatus()

  // 监听安装提示
  window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)

  // 监听网络状态
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)

  // 定期检查Service Worker状态
  const interval = setInterval(checkSWStatus, 5000)

  onUnmounted(() => {
    clearInterval(interval)
  })
})

const visible = ref(false)
const scannerValue = ref('-')

// 开启相机
function handleOpenCamera() {
  visible.value = true
  console.log('🚀 ~ handleOpenCamera ~ visible:', visible.value)
}

// 读取扫码结果
function handleScannerData(data) {
  scannerValue.value = data
}

onUnmounted(() => {
  window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)
})
</script>

<template>
  <div class="pwa-test">
    <div class="container">
      <h1>PWA 功能测试</h1>

      <div class="status-section">
        <h2>Service Worker 状态</h2>
        <div class="status-item">
          <span class="label">状态:</span>
          <span class="value" :class="swStatus">{{ swStatusText }}</span>
        </div>
        <div class="status-item">
          <span class="label">注册时间:</span>
          <span class="value">{{ swRegistrationTime || '未注册' }}</span>
        </div>
      </div>

      <div class="feature-section">
        <h2>PWA 功能</h2>

        <div class="feature-item">
          <h3>安装提示</h3>
          <p>如果您的设备支持PWA安装，将会显示安装提示</p>
          <button v-if="canInstall" class="install-btn" @click="installApp">
            安装应用
          </button>
          <p v-else class="note">
            您的设备不支持PWA安装或应用已安装
          </p>
        </div>

        <div class="feature-item">
          <h3>离线支持</h3>
          <p>应用支持离线访问已缓存的内容</p>
          <div class="offline-status">
            <span class="label">网络状态:</span>
            <span class="value" :class="networkStatus">
              {{ networkStatus === 'online' ? '在线' : '离线' }}
            </span>
          </div>
        </div>

        <div class="feature-item">
          <h3>更新检测</h3>
          <p>自动检测应用更新并提示用户刷新</p>
          <button v-if="hasUpdate" class="update-btn" @click="updateApp">
            更新应用
          </button>
          <p v-else class="note">
            应用已是最新版本
          </p>
        </div>
      </div>

      <div class="info-section">
        <h2>PWA 信息</h2>
        <div class="info-item">
          <span class="label">应用名称:</span>
          <span class="value">Stella Agent</span>
        </div>
        <div class="info-item">
          <span class="label">版本:</span>
          <span class="value">1.0.0</span>
        </div>
        <div class="info-item">
          <span class="label">主题色:</span>
          <span class="value">
            <span class="color-preview" :style="{ backgroundColor: '#4F46E5' }" />
            #4F46E5
          </span>
        </div>
      </div>

      <div class="info-section">
        <h2>扫码测试</h2>
        <div class="info-item">
          <span class="label">相机状态:</span>
          <span class="value">
            <el-button @click="handleOpenCamera">开启相机</el-button>
          </span>
        </div>
        <div class="info-item">
          <span class="label">扫码结果:</span>
          <span class="value">{{ scannerValue }}</span>
        </div>
      </div>

      <ScannerComponent v-model:visible="visible" @code="handleScannerData" />
    </div>
  </div>
</template>

<style scoped>
.pwa-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

h1 {
  color: #4f46e5;
  text-align: center;
  margin-bottom: 32px;
  font-size: 28px;
}

h2 {
  color: #333;
  margin-bottom: 16px;
  font-size: 20px;
  border-bottom: 2px solid #4f46e5;
  padding-bottom: 8px;
}

.status-section,
.feature-section,
.info-section {
  margin-bottom: 32px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-item,
.feature-item,
.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
}

.label {
  font-weight: 600;
  color: #555;
  min-width: 120px;
  margin-right: 12px;
}

.value {
  color: #333;
}

.status-item .value {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.status-item .value.registered {
  background: #d4edda;
  color: #155724;
}

.status-item .value.not-registered {
  background: #f8d7da;
  color: #721c24;
}

.status-item .value.error {
  background: #f8d7da;
  color: #721c24;
}

.status-item .value.not-supported {
  background: #fff3cd;
  color: #856404;
}

.status-item .value.unknown {
  background: #e2e3e5;
  color: #383d41;
}

.install-btn,
.update-btn {
  background: #4f46e5;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  margin-top: 8px;
  transition: background-color 0.2s;
}

.install-btn:hover,
.update-btn:hover {
  background: #4338ca;
}

.note {
  color: #666;
  font-style: italic;
  margin-top: 8px;
}

.offline-status {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.network-status .value.online {
  color: #28a745;
  font-weight: 600;
}

.network-status .value.offline {
  color: #dc3545;
  font-weight: 600;
}

.color-preview {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 4px;
  margin-right: 8px;
  vertical-align: middle;
  border: 1px solid #ddd;
}

@media (max-width: 768px) {
  .pwa-test {
    padding: 16px;
  }

  .container {
    padding: 16px;
  }

  .status-item,
  .feature-item,
  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .label {
    margin-bottom: 4px;
  }
}
</style>
