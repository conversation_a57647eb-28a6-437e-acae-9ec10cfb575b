/**
 * 语言列表
 * https://blog.csdn.net/u010586698/article/details/56673379
 */
export const languageList = [
  {
    label: '中文(简体)',
    value: 'zh_CN',
  },
  {
    label: 'English',
    value: 'en_US',
  },
  {
    label: '中文(繁体)',
    value: 'zh_TW',
  },
  {
    label: 'tiếng việt', // 越南语 Vietnamese
    value: 'vi_VN',
  },
  {
    label: 'Indonesian', // 印度尼西亚语
    value: 'in_ID',
  },
]

/**
 * 错误提示信息
 */
export const errorMessage = {
  401: '认证失败，无法访问系统资源',
  403: '当前操作没有权限',
  404: '访问资源不存在',
  default: '系统未知错误，请联系管理员',
}

/**
 * 主题色
 */
export const themeColor = '#8e6f47'

/**
 * API接口 Host地址
 */
export const apiHost = 'http://***********:8080'

/**
 * 路由白名单 - 不需要登录验证的页面
 */
export const whiteList = ['/login', '/401', '/404']
