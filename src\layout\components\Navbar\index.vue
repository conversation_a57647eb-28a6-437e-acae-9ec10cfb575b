<script setup>
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElIcon, ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown, SwitchButton } from '@element-plus/icons-vue'

import ChatHistoryComponent from '@/components/ChatHistoryComponent/index.vue'
import SettingComponent from '@/components/SettingComponent/index.vue'
import router from '@/router'
import { useUserStore } from '@/store'

const historyVisible = ref(false)
const settingVisible = ref(false)

const userStore = useUserStore()

// 返回首页
function handleClickHomeEvent() {
  router.push('/index')
}

// 历史记录打开和关闭
function handleClickHistoryEvent() {
  historyVisible.value = true
}

// 设置界面打开
function handleClickSettingEvent() {
  settingVisible.value = true
}

// 用户下拉菜单点击事件
function handleUserMenuClick(command) {
  if (command === 'logout') {
    handleLogout()
  }
}

// 登出处理
async function handleLogout() {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await userStore.logout()
    ElMessage.success('退出登录成功')
    router.push('/login')
  }
  catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('退出登录失败')
    }
  }
}
</script>

<template>
  <div class="navbar-wrapper">
    <div class="left">
      <div class="icon" @click="handleClickHomeEvent">
        <img src="@/assets/svg/icon3.svg" alt="Logo">
      </div>
      <div class="logo" @click="handleClickHomeEvent">
        <span class="color1">SMART</span>
        <span>STELLA</span>
      </div>
    </div>
    <div class="right">
      <div class="btn icon history" @click="handleClickHistoryEvent">
        <img src="@/assets/svg/icon-history.svg" alt="history">
      </div>
      <div class="btn icon" @click="handleClickSettingEvent">
        <img src="@/assets/svg/icon-setting.svg" alt="setting">
      </div>

      <!-- 用户信息下拉菜单 -->
      <el-dropdown class="user-dropdown" @command="handleUserMenuClick">
        <div class="user-info">
          <span class="username">{{ userStore.nickname || userStore.username || '用户' }}</span>
          <el-icon class="arrow-down">
            <ArrowDown />
          </el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>

  <SettingComponent v-model:visible="settingVisible" />
  <ChatHistoryComponent v-model:visible="historyVisible" />
</template>

<style lang="less" scoped>
.navbar-wrapper {
  width: 100%;
  height: @navbar-height;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.15);
  position: relative;
  user-select: none;

  &.sidebar-fold {
    .left {
      .icon {
        transform: rotate(80deg);
      }
    }
  }

  .left {
    width: @sidebar-width;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon {
      width: 35px;
      height: 35px;
      margin-right: 15px;
      cursor: pointer;
    }

    .logo {
      font-size: 28px;
      color: #374151;
      cursor: pointer;

      .color1 {
        color: #8e6f47;
        margin-right: 10px;
      }
    }
  }

  .right {
    display: flex;
    align-items: center;
    margin-right: 40px;

    .btn {
      width: 35px;
      height: 35px;
      opacity: 0.75;
      transition: opacity 0.3s;
      cursor: pointer;

      &:hover {
        opacity: 1;
      }
    }

    .icon {
      padding: 1px;

      &.history {
        transform: scale(0.85);
      }

      &:first-child {
        margin-right: 20px;
      }
    }

    .user-dropdown {
      margin-left: 20px;

      .user-info {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 8px 12px;
        border-radius: 6px;
        transition: background-color 0.3s;

        &:hover {
          background-color: rgba(142, 111, 71, 0.1);
        }

        .username {
          font-size: 14px;
          color: #374151;
          margin-right: 6px;
          font-weight: 500;
        }

        .arrow-down {
          font-size: 12px;
          color: #8e6f47;
        }
      }
    }
  }
}
</style>
