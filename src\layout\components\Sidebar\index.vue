<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'

import router from '@/router'
import { storeSystem, useUserStore } from '@/store'

const route = useRoute()
const _storeSystem = storeSystem()
const userStore = useUserStore()
const activeSidebar = ref('ioe-database')
const sidebarItems = ref([
  {
    id: 'ioe-database',
    label: 'IOE知识库',
    describe: '关于IOE设备相知识库',
    path: '/index',
  },
  {
    id: 'ioe-graph',
    label: 'IOE知识图谱',
    describe: '了解IOE资产设备关系',
    path: '/graph',
  },
  {
    id: 'ioe-document',
    label: 'IOE资源检索',
    describe: '检索所有包含文档、视频等相关资源',
    path: '/document',
  },
  {
    id: 'pwa-test',
    label: 'PWA测试界面',
    describe: 'PWA测试界面',
    path: '/testPwa',
  },
  {
    id: 'voice-test',
    label: 'Voice测试界面',
    describe: '讯飞语音测试界面',
    path: '/testVoice',
  },
])

watch(
  () => route.path,
  (data) => {
    const item = sidebarItems.value.find(item => item.path === data)
    if (item)
      activeSidebar.value = item.id
  },
)

// 跳转链接地址
function handleClickSidebarEvent(id) {
  const item = sidebarItems.value.find(item => item.id === id)
  if (!item)
    return

  router.push(item.path)
  activeSidebar.value = item.id
}

// 切换侧边栏展开和收起状态
function handleToggleSidebarEvent() {
  _storeSystem.sidebarFold = !_storeSystem.sidebarFold
}

// 登出处理
async function handleLogout() {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await userStore.logout()
    ElMessage.success('退出登录成功')
    router.push('/login')
  }
  catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('退出登录失败')
    }
  }
}

onMounted(() => {
  // 初始化激活项
  const item = sidebarItems.value.find(item => item.path === route.path)
  if (item)
    activeSidebar.value = item.id
})
</script>

<template>
  <div class="sidebar-wrapper">
    <div class="header">
      <div class="title">
        <div class="label">
          知识库
        </div>
        <div class="button" @click="handleToggleSidebarEvent">
          <img src="@/assets/svg/icon-arrow.svg" alt="logo">
        </div>
      </div>

      <div class="tips">
        选择一个知识库已获得更专业的回答
      </div>
    </div>

    <div class="sidebar-menu global-scrollbar">
      <div
        v-for="item in sidebarItems" :key="item.path" :class="{ active: activeSidebar === item.id }" class="item"
        @click="handleClickSidebarEvent(item.id)"
      >
        <div class="icon">
          <img v-if="activeSidebar === item.id" src="@/assets/svg/icon1.svg" alt="logo">
          <img v-else src="@/assets/svg/icon2.svg" alt="logo">
        </div>
        <div class="content">
          <div class="label">
            {{ item.label }}
          </div>
          <div class="describe">
            {{ item.describe }}
          </div>
        </div>
      </div>
    </div>

    <!-- 登出按钮 -->
    <div class="logout-section">
      <div class="logout-button" @click="handleLogout">
        <div class="icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M16 17L21 12L16 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M21 12H9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="content">
          <div class="label">退出登录</div>
          <div class="describe">安全退出系统</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.sidebar-wrapper {
  background-color: #f2f1f0;
  width: @sidebar-width;
  height: calc(100vh - @navbar-height);
  display: flex;
  flex-direction: column;
  transition: all 0.25s ease-in-out;

  .header {
    .title {
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #cac3b8;
      overflow: hidden;

      .label {
        font-size: 30px;
        font-weight: bold;
        margin-left: 30px;
      }

      .button {
        width: 60px;
        height: 100%;
        background-color: #dedede;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.2s ease-in-out;
        user-select: none;

        &:hover {
          background-color: #cac3b8;
        }

        img {
          width: 25px;
          height: 25px;
          object-fit: contain;
        }
      }
    }

    .tips {
      padding: 20px 10px;
      text-align: center;
      color: #808080;
      font-size: 12px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .sidebar-menu {
    flex: 1;
    overflow: auto;

    .item {
      box-sizing: border-box;
      cursor: pointer;
      display: flex;
      padding: 15px 5px 15px 0;
      border-top: 1px solid #cac3b8;
      transition: all 0.3s ease-in-out;
      user-select: none;

      &.active {
        border-left: 10px solid #8e6f47;
        background-color: #eae1d4 !important;

        .icon {
          margin-left: 10px;
        }
      }

      &:hover {
        background-color: #eae1d484;
      }

      &:last-child {
        border-bottom: 1px solid #cac3b8;
      }

      .icon {
        display: flex;
        align-items: center;
        width: 50px;
        margin-left: 20px;

        img {
          width: 38px;
          height: 38px;
          object-fit: contain;
        }
      }

      .content {
        user-select: none;
        margin-left: 5px;
        flex: 1;
        min-width: 0;

        .label {
          color: #374151;
          font-size: 16px;
          margin-bottom: 4px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .describe {
          color: #808080;
          font-size: 14px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }

  .logout-section {
    border-top: 2px solid #cac3b8;
    padding: 0;

    .logout-button {
      box-sizing: border-box;
      cursor: pointer;
      display: flex;
      padding: 15px 5px 15px 0;
      transition: all 0.3s ease-in-out;
      user-select: none;
      background-color: #f2f1f0;

      &:hover {
        background-color: #ffe6e6;

        .content .label {
          color: #d32f2f;
        }

        .icon svg {
          color: #d32f2f;
        }
      }

      .icon {
        display: flex;
        align-items: center;
        width: 50px;
        margin-left: 20px;

        svg {
          width: 24px;
          height: 24px;
          color: #666;
          transition: color 0.3s ease-in-out;
        }
      }

      .content {
        user-select: none;
        margin-left: 5px;
        flex: 1;
        min-width: 0;

        .label {
          color: #666;
          font-size: 16px;
          margin-bottom: 4px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          transition: color 0.3s ease-in-out;
        }

        .describe {
          color: #999;
          font-size: 14px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
</style>
