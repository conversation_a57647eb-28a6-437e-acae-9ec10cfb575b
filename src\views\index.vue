<script setup>
import { ref } from 'vue'
import { useUserStore } from '@/store'

const userStore = useUserStore()

// 状态选择器
const statusOptions = [
  { label: '全部', value: 'all' },
  { label: '工作中', value: 'working' },
  { label: '关闭', value: 'closed' },
  { label: '待机中', value: 'standby' }
]
const selectedStatus = ref('all')

// 设备编号选择器
const deviceOptions = [
  { label: '全部', value: 'all' },
  { label: 'PA2HDC17017', value: 'PA2HDC17017' },
  { label: 'PA2HDC17018', value: 'PA2HDC17018' },
  { label: 'PA2HDC17019', value: 'PA2HDC17019' },
  { label: 'PA2HDC17020', value: 'PA2HDC17020' }
]
const selectedDevice = ref('all')

// 设备数据
const deviceList = ref([
  {
    id: 'PA2HDC17017',
    name: 'PA2HDC17017',
    status: 'working',
    statusText: '工作中',
    statusColor: '#52c41a',
    model: 'BA109388',
    process: '针车/后帮前帮缝制...',
    operator: '张小伟',
    startTime: '2025-08-19 08:02',
    image: '/device-placeholder.png'
  },
  {
    id: 'PA2HDC17018',
    name: 'PA2HDC17018',
    status: 'closed',
    statusText: '关闭',
    statusColor: '#ff4d4f',
    model: 'BA109388',
    process: '半成品/1、缝条花...',
    operator: '张小伟',
    startTime: '2025-08-19 08:02',
    image: '/device-placeholder.png'
  },
  {
    id: 'PA2HDC17019',
    name: 'PA2HDC17019',
    status: 'standby',
    statusText: '待机中',
    statusColor: '#faad14',
    model: 'BA109388',
    process: '半成品/大底风刀...',
    operator: '张小伟',
    startTime: '2025-08-19 08:02',
    image: '/device-placeholder.png'
  },
  {
    id: 'PA2HDC17020',
    name: 'PA2HDC17020',
    status: 'standby',
    statusText: '待机中',
    statusColor: '#faad14',
    model: 'BA109388',
    process: '印刷',
    operator: '张小伟',
    startTime: '2025-08-19 08:02',
    image: '/device-placeholder.png'
  }
])

// 功能卡片点击事件
function handleCardClick(type) {
  console.log('点击功能卡片:', type)
  // 这里可以添加具体的跳转逻辑
}

// 添加设备
function handleAddDevice() {
  console.log('添加设备')
  // 这里可以添加添加设备的逻辑
}
</script>

<template>
  <div class="index-wrapper">
    <!-- 用户欢迎信息 -->
    <div class="welcome-section">
      <h2 class="welcome-text">Hi, {{ userStore.nickname || userStore.username || 'admin' }}</h2>
    </div>

    <!-- 功能卡片区域 -->
    <div class="function-cards">
      <div class="card" @click="handleCardClick('evaluation')">
        <div class="card-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="card-title">新建测评</div>
      </div>

      <div class="card" @click="handleCardClick('realtime')">
        <div class="card-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3 3H5C5.55228 3 6 3.44772 6 4V20C6 20.5523 5.55228 21 5 21H3C2.44772 21 2 20.5523 2 20V4C2 3.44772 2.44772 3 3 3Z" stroke="currentColor" stroke-width="2"/>
            <path d="M9 8H11C11.5523 8 12 8.44772 12 9V20C12 20.5523 11.5523 21 11 21H9C8.44772 21 8 20.5523 8 20V9C8 8.44772 8.44772 8 9 8Z" stroke="currentColor" stroke-width="2"/>
            <path d="M15 13H17C17.5523 13 18 13.4477 18 14V20C18 20.5523 17.5523 21 17 21H15C14.4477 21 14 20.5523 14 20V14C14 13.4477 14.4477 13 15 13Z" stroke="currentColor" stroke-width="2"/>
            <path d="M21 6H19C18.4477 6 18 6.44772 18 7V20C18 20.5523 18.4477 21 19 21H21C21.5523 21 22 20.5523 22 20V7C22 6.44772 21.5523 6 21 6Z" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="card-title">实时状态</div>
      </div>

      <div class="card" @click="handleCardClick('analysis')">
        <div class="card-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M16 4V10C16 10.5523 16.4477 11 17 11H23M16 4L23 11M16 4H6C4.89543 4 4 4.89543 4 6V18C4 19.1046 4.89543 20 6 20H18C19.1046 20 20 19.1046 20 18V11M16 4V10C16 10.5523 16.4477 11 17 11H23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M8 12H12M8 16H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </div>
        <div class="card-title">OEE分析</div>
      </div>
    </div>

    <!-- 筛选器区域 -->
    <div class="filter-section">
      <div class="filter-item">
        <label>状态</label>
        <el-select v-model="selectedStatus" placeholder="全部">
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>

      <div class="filter-item">
        <label>设备编号</label>
        <el-select v-model="selectedDevice" placeholder="全部">
          <el-option
            v-for="item in deviceOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
    </div>

    <!-- 设备列表 -->
    <div class="device-list">
      <div v-for="device in deviceList" :key="device.id" class="device-item">
        <div class="device-image">
          <div class="device-placeholder">
            <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#ccc" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>

        <div class="device-info">
          <div class="device-header">
            <h3 class="device-name">{{ device.name }}</h3>
            <div class="device-status" :style="{ color: device.statusColor }">
              <span class="status-dot" :style="{ backgroundColor: device.statusColor }"></span>
              {{ device.statusText }}
            </div>
          </div>

          <div class="device-details">
            <div class="detail-row">
              <span class="label">型体工序</span>
              <span class="value">{{ device.model }}</span>
            </div>
            <div class="detail-row">
              <span class="label">操作人员</span>
              <span class="value">{{ device.process }}</span>
            </div>
            <div class="detail-row">
              <span class="label">上机时间</span>
              <span class="value">{{ device.operator }}</span>
            </div>
            <div class="detail-row">
              <span class="label"></span>
              <span class="value">{{ device.startTime }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加按钮 -->
    <div class="add-button" @click="handleAddDevice">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>
  </div>
</template>

<style lang="less" scoped>
.index-wrapper {
  width: 100%;
  padding: 20px;
  min-height: 100%;
  box-sizing: border-box;
  background-color: #f5f5f5;
  position: relative;

  .welcome-section {
    margin-bottom: 30px;

    .welcome-text {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }

  .function-cards {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;

    .card {
      flex: 1;
      background: #4a4a4a;
      border-radius: 12px;
      padding: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      color: white;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      min-height: 120px;
      justify-content: center;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      .card-icon {
        margin-bottom: 12px;
        color: #E8A05D;

        svg {
          width: 32px;
          height: 32px;
        }
      }

      .card-title {
        font-size: 16px;
        font-weight: 500;
        color: #E8A05D;
      }
    }
  }

  .filter-section {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;

    .filter-item {
      display: flex;
      flex-direction: column;
      gap: 8px;

      label {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }

      :deep(.el-select) {
        width: 150px;

        .el-input__wrapper {
          border-radius: 8px;
          border: 1px solid #ddd;

          &:hover {
            border-color: #E8A05D;
          }

          &.is-focus {
            border-color: #E8A05D;
            box-shadow: 0 0 0 2px rgba(232, 160, 93, 0.2);
          }
        }
      }
    }
  }

  .device-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 80px;

    .device-item {
      background: white;
      border-radius: 12px;
      padding: 15px;
      display: flex;
      gap: 15px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        transform: translateY(-1px);
      }

      .device-image {
        width: 80px;
        height: 80px;
        border-radius: 8px;
        overflow: hidden;
        background: #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: center;

        .device-placeholder {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);

          svg {
            opacity: 0.6;
          }
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .device-info {
        flex: 1;
        display: flex;
        flex-direction: column;

        .device-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 12px;

          .device-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0;
          }

          .device-status {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            font-weight: 500;

            .status-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
            }
          }
        }

        .device-details {
          display: flex;
          flex-direction: column;
          gap: 6px;

          .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .label {
              font-size: 14px;
              color: #666;
              min-width: 80px;
            }

            .value {
              font-size: 14px;
              color: #333;
              text-align: right;
              flex: 1;
            }
          }
        }
      }

    }
  }

  .add-button {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 56px;
    height: 56px;
    background: #4a4a4a;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    color: #E8A05D;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
      background: #3a3a3a;
    }

    &:active {
      transform: translateY(0);
    }

    svg {
      width: 24px;
      height: 24px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 15px;

    .function-cards {
      flex-direction: column;
      gap: 15px;

      .card {
        min-height: 100px;
      }
    }

    .filter-section {
      flex-direction: column;
      gap: 15px;

      .filter-item {
        :deep(.el-select) {
          width: 100%;
        }
      }
    }

    .device-item {
      flex-direction: column;

      .device-image {
        width: 100%;
        height: 120px;
      }

      .device-info {
        .device-header {
          flex-direction: column;
          gap: 8px;
          align-items: flex-start;
        }
      }
    }

    .add-button {
      bottom: 20px;
      right: 20px;
      width: 48px;
      height: 48px;

      svg {
        width: 20px;
        height: 20px;
      }
    }
  }
}
</style>
