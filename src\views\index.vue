<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, ref } from 'vue'
import useClipboard from 'vue-clipboard3'

import { chat_completions, chat_completions_streams } from '@/api/chat'
import ChatInputComponent from '@/components/ChatInputComponent/index.vue'
import VoiceInputComponent from '@/components/VoiceInputComponent/index.vue'
import { isEmptyWithString, nanoId } from '@/utils/common'

const { toClipboard } = useClipboard()

const inputValue = ref('')
const voiceStatus = ref(false)
const chatMessages = ref([])
const stream = ref(true)
const isStreaming = ref(false) // 添加流式传输状态

// 数据回显事件
function handleVoiceInputDataEvent(data) {
  inputValue.value = data
}

// 语音输入点击事件
function handleVoiceInputClickEvent() {
  if (!voiceStatus.value) {
    console.log('开启语音输入模式')
    voiceStatus.value = true
  }
  else {
    console.log('停止语音输入模式')
    voiceStatus.value = false
  }

  console.log('🚀 ~ handleVoiceInputClickEvent ~ voiceStatus:', voiceStatus)
}

// 发送消息事件
function handleSendInputValueEvent() {
  if (isEmptyWithString(inputValue.value)) {
    ElMessage({ message: '请输入内容后重试', type: 'warning', plain: true })
    return
  }

  // 拷贝文本内容
  const content = `${inputValue.value}`
  const userChat = { id: nanoId(32, 'user_'), type: 'user', role: 'User', content }
  chatMessages.value.push(userChat)
  inputValue.value = ''

  const param = { message: content, use_mcp: false, stream: stream.value }
  if (!stream.value) {
    // API返回
    chat_completions(param).then((response) => {
      console.log('🚀 ~ handleChatMessageEvent ~ response:', response)
      const assistantChat = {
        id: nanoId(32, 'assistant_'),
        type: 'assistant',
        role: 'Assistant',
        content: response.message,
      }
      chatMessages.value.push(assistantChat)
    })
  }
  else {
    // 流返回
    isStreaming.value = true // 设置流式传输状态

    // 先创建一个空的助手消息
    const assistantChat = {
      id: nanoId(32, 'assistant_'),
      type: 'assistant',
      role: 'Assistant',
      content: '',
    }
    chatMessages.value.push(assistantChat)

    chat_completions_streams(
      param,
      // onMessage 回调：处理每个流式消息
      (data) => {
        console.log('🚀 ~ 流式消息 ~ data:', data)
        if (data.message) {
          // 更新助手消息内容
          assistantChat.content = data.message
        }
      },
      // onComplete 回调：流式传输完成
      () => {
        console.log('🚀 ~ 流式传输完成')
        isStreaming.value = false // 重置流式传输状态
        // 可以在这里做一些完成后的处理
      },
      // onError 回调：处理错误
      (error) => {
        console.error('🚀 ~ 流式传输错误 ~ error:', error)
        isStreaming.value = false // 重置流式传输状态
        ElMessage({ message: '流式传输出现错误', type: 'error', plain: true })
      },
    )
  }
}

// 新对话按钮
function handleNewChatEvent() {
  ElMessageBox.confirm('确定要开启新的对话吗？当前对话将被清空。', '开起新对话', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
  })
    .then((data) => {
      console.log('🚀 ~ handleNewChatEvent ~ data:', data)
      initChatMessageWithSysteam()
    })
    .catch(() => {
    })
}

// 初始化消息对话列表
function initChatMessageWithSysteam() {
  const systemChat = {
    id: nanoId(32, 'sys_'),
    type: 'system',
    role: 'System',
    content: '您好！我是兴云智能体(Xingyun Stella Agent)，一个实用的AI助手。有什么我可以帮您的吗？',
  }
  chatMessages.value.push(systemChat)
}

// 消息按钮事件
async function handleChatMessageOptionEvent(type, data) {
  console.log('handleChatMessageOptionEvent', type, data)

  if (type === 'copy') {
    try {
      await toClipboard(data.content)
      ElMessage({ message: '复制成功', type: 'success', plain: true })
    }
    catch (e) {
      ElMessage({ message: '复制失败，请重试。', type: 'error', plain: true })
      console.log('🚀 ~ handleChatMessageOptionEvent ~ e:', e)
    }
    return
  }

  if (type === 'mail') {
  }

  if (type === 'teams') {
  }
}

onMounted(() => {
  initChatMessageWithSysteam()
})
</script>

<template>
  <div class="index-wrapper">
    <div class="option-content">
      <div class="btn-new-chat" @click="handleNewChatEvent">
        开启新对话
      </div>
    </div>

    <div class="chat-content">
      <div v-for="chat in chatMessages" :key="chat.id" class="message" :class="chat.type">
        <div class="inner">
          <div class="meta">
            <span class="role">{{ chat.role }}</span>
          </div>
          <div class="bubble">
            {{ chat.content }}
            <!-- 流式传输状态指示器 - 只在最后一条助手消息且正在流式传输时显示 -->
            <div
              v-if="isStreaming && chat.type === 'assistant' && chat.id === chatMessages[chatMessages.length - 1]?.id"
              class="streaming-indicator-inline"
            >
              <div class="streaming-text">
                AI正在思考中...
              </div>
              <div class="streaming-dots">
                <span />
                <span />
                <span />
              </div>
            </div>
          </div>

          <div class="expand-content">
            <div v-if="chat.suggestions && chat.suggestions.length > 0" class="suggestion">
              <div class="label">
                相关建议
              </div>
              <div class="content">
                <div v-for="suggestion in chat.suggestions" :key="suggestion.id" class="item">
                  {{ suggestion.content }}
                </div>
              </div>
            </div>

            <div v-if="chat.type !== 'system'" class="options">
              <div class="btn copy" @click="handleChatMessageOptionEvent('copy', chat)">
                <img src="@/assets/svg/icon-copy.svg" alt="copy">
              </div>
              <div class="btn mail" @click="handleChatMessageOptionEvent('mail', chat)">
                <img src="@/assets/svg/icon-mail.svg" alt="mail">
              </div>
              <div class="btn teams" @click="handleChatMessageOptionEvent('teams', chat)">
                <img src="@/assets/svg/icon-teams.svg" alt="teams">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <ChatInputComponent
      v-model:value="inputValue"
      :disabled="isStreaming"
      @btn-send="handleSendInputValueEvent"
      @btn-voice="handleVoiceInputClickEvent"
      @btn-cancel="() => {}"
    />
    <div class="mask" />
    <VoiceInputComponent v-model:visible="voiceStatus" @data="handleVoiceInputDataEvent" />
  </div>
</template>

<style lang="less" scoped>
.index-wrapper {
  width: 100%;
  padding: 0 @app-main-padding-left-right;
  min-height: 100%;
  box-sizing: border-box;
  background-color: #f9f9f9;
  position: relative;

  .option-content {
    position: fixed;
    top: calc(@navbar-height + 40px);
    right: 40px;
    z-index: 2;

    .btn-new-chat {
      color: #111827;
      cursor: pointer;
      opacity: 0.75;
      transition: all 0.3s;
      background-color: #fff;
      padding: 8px 15px;
      border-radius: 8px;
      box-shadow: 0 0 10px 0 #0000001a;
      user-select: none;

      &:hover {
        opacity: 1;
      }
    }
  }

  .chat-content {
    padding: 20px 0;
    min-height: calc(100vh - 20px - @navbar-height);

    .message {
      display: flex;
      margin-bottom: 20px;
      justify-content: flex-start;

      &:hover {
        .expand-content {
          .options {
            opacity: 1;
          }
        }
      }

      &.user {
        justify-content: flex-end !important;

        .inner {
          padding: 5px 0 10px 0;
          background-color: #eae1d4;
          border: 1px solid #eae1d4;

          &::before {
            left: unset;
            right: -8px;
            top: 28px;
            border-color: transparent transparent transparent #eae1d4;
          }

          &::after {
            left: unset;
            right: -7px;
            top: 28px;
            border-width: 8px 0 8px 8px;
            border-color: transparent transparent transparent #eae1d4;
          }

          .meta {
            display: none;
          }

          .expand-content {
            .suggestion {
              display: none;
            }

            .options {
              left: unset;
              right: 15px;
              bottom: -25px;
            }
          }
        }
      }

      .inner {
        min-width: 150px;
        border-radius: 8px;
        background: #ffffff;
        border: 1px solid #e5e7eb;
        max-width: 82%;
        display: flex;
        flex-direction: column;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: -8px;
          top: 28px;
          width: 0;
          height: 0;
          border-style: solid;
          border-width: 8px 8px 8px 0;
          border-color: transparent #e5e7eb transparent transparent;
        }

        &::after {
          content: '';
          position: absolute;
          left: -7px;
          top: 28px;
          width: 0;
          height: 0;
          border-style: solid;
          border-width: 8px 8px 8px 0;
          border-color: transparent #ffffff transparent transparent;
        }

        .meta {
          margin: 8px 15px 0 15px;
          font-size: 16px;
          color: #808080;
        }

        .bubble {
          color: #333;
          margin: 10px 15px;
          font-size: 14px;
          line-height: 1.6;
        }
      }

      .expand-content {
        position: relative;

        .suggestion {
          border-radius: 6px;
          margin: 0 10px 25px 10px;
          padding: 8px 10px 0 10px;
          background-color: #f2f1f0;

          .label {
            font-size: 12px;
            font-weight: bold;
            color: #374151;
            margin-bottom: 5px;
          }

          .content {
            display: flex;
            flex-direction: column;

            .item {
              border-radius: 6px;
              font-size: 12px;
              color: #808080;
              cursor: pointer;
              transition: all 0.2s ease;
              padding: 6px 8px;
              background-color: #fff;
              margin-bottom: 8px;
              line-height: 1.6;
              border: 1px solid #fff;

              &:hover {
                border-color: #c7d2fe;
              }
            }
          }
        }

        .options {
          position: absolute;
          bottom: -15px;
          display: flex;
          margin-left: 15px;
          opacity: 0;
          transition: opacity 0.25s;

          .btn {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 20px;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 6px;

            &:hover {
              box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
            }

            &:last-child {
              margin-right: 0;
            }

            img {
              width: 16px;
            }
          }
        }
      }
    }
  }

  .chat-input-wrapper {
    width: 100%;
    position: sticky;
    bottom: 30px;
    z-index: 1;
  }

  .mask {
    width: 100%;
    height: 90px;
    position: sticky;
    bottom: 0;
    background: linear-gradient(180deg, rgba(248, 248, 248, 0) 0%, #f8f8f8 100%);
  }

  .streaming-indicator {
    position: fixed;
    top: calc(@navbar-height + 40px);
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    padding: 10px 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 10px;
    backdrop-filter: blur(5px);

    .streaming-text {
      font-size: 14px;
      color: #333;
      font-weight: bold;
    }

    .streaming-dots {
      display: flex;
      gap: 5px;

      span {
        display: inline-block;
        width: 8px;
        height: 8px;
        background-color: #4f46e5;
        border-radius: 50%;
        animation: dot-pulse 1.4s infinite ease-in-out;

        &:nth-child(1) {
          animation-delay: -0.32s;
        }

        &:nth-child(2) {
          animation-delay: -0.16s;
        }

        &:nth-child(3) {
          animation-delay: 0s;
        }
      }
    }
  }

  // 内联流指示器样式
  .streaming-indicator-inline {
    margin-top: 8px;
    padding: 8px 12px;
    background-color: rgba(156, 163, 175, 0.08);
    border-radius: 6px;
    border-left: 2px solid rgba(156, 163, 175, 0.3);
    display: flex;
    align-items: center;
    gap: 8px;

    .streaming-text {
      font-size: 12px;
      color: rgba(107, 114, 128, 0.8);
      font-weight: 400;
    }

    .streaming-dots {
      display: flex;
      gap: 3px;

      span {
        display: inline-block;
        width: 6px;
        height: 6px;
        background-color: rgba(156, 163, 175, 0.6);
        border-radius: 50%;
        animation: dot-pulse 1.4s infinite ease-in-out;

        &:nth-child(1) {
          animation-delay: -0.32s;
        }

        &:nth-child(2) {
          animation-delay: -0.16s;
        }

        &:nth-child(3) {
          animation-delay: 0s;
        }
      }
    }
  }

  @keyframes dot-pulse {
    0%,
    80%,
    100% {
      transform: translateY(0);
      opacity: 0.3;
    }

    40% {
      transform: translateY(-4px);
      opacity: 0.8;
    }
  }
}
</style>
