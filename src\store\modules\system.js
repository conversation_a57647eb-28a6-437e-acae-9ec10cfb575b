import { defineStore } from 'pinia'

export const storeSystem = defineStore('storeSystem', {
  state: () => ({
    _init: false,
    language: 'zh_CN',
    sidebarFold: false,
  }),
  actions: {
    init() {
      if (this._init)
        return
      console.log('init store => storeSystem')
      this.default()
      this._init = true
      this.sidebarFold = false
    },
    /**
     * 重置为默认
     */
    default() {
      console.log('reset default => storeSystem')
      this._init = false
      this.sidebarFold = false
    },
  },
  persist: {
    enabled: true,
    strategies: [{ storage: localStorage }],
    deserialize: JSON.parse,
    serialize: JSON.stringify,
  },
})
