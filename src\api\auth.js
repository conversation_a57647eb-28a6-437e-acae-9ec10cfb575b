import request from '@/utils/request'

/**
 * 用户登录
 * @param {Object} data 登录数据
 * @param {string} data.staffCode 员工代码
 * @param {string} data.password 密码
 */
export function login(data) {
  // 创建FormData对象
  const formData = new FormData()
  formData.append('staffCode', data.staffCode)
  formData.append('password', data.password)

  return request({
    url: '/esg-ims-vue/portalApi/portal/auth/loginAuthOEE.action',
    method: 'POST',
    data: formData,
  })
}

/**
 * 用户登出
 */
export function logout() {
  return request({
    url: '/agent-api/auth/logout',
    method: 'POST',
  })
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return request({
    url: '/agent-api/auth/userinfo',
    method: 'GET',
  })
}

/**
 * 刷新token
 * @param {string} refreshToken 刷新token
 */
export function refreshToken(refreshToken) {
  return request({
    url: '/agent-api/auth/refresh',
    method: 'POST',
    data: { refreshToken },
  })
}
