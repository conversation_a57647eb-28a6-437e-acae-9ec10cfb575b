import Cookies from 'js-cookie'

import { whiteList } from '@/config/constant'

const TokenKey = 'Admin-Token'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function getRefreshToken() {
  return Cookies.get('Refresh-Token')
}

export function setRefreshToken(token) {
  return Cookies.set('Refresh-Token', token)
}

export function removeRefreshToken() {
  return Cookies.remove('Refresh-Token')
}

export function clearAuth() {
  removeToken()
  removeRefreshToken()
}

/**
 * 检查路径是否在白名单中
 * @param {string} path 路径
 * @returns {boolean} 是否在白名单中
 */
export function whiteListCheck(path) {
  return whiteList.includes(path)
}
