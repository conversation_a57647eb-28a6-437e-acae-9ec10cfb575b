# OEE项目登录功能实现说明

## 功能概述

已成功为OEE项目添加了完整的登录功能，包括登录页面、用户认证、路由守卫和登出功能。登录页面已按照UI设计图重新设计，并集成了真实的登录API。

## 实现的功能

### 1. 登录页面 (`src/views/login.vue`)

- **设计风格**: 完全按照UI设计图实现
  - 橙色渐变背景 (#E8A05D → #D4935A → #C08556)
  - 白色大标题 "OEE" 和副标题 "Evaluation System"
  - 半透明圆角输入框
  - 深色圆角登录按钮
- **表单验证**: 账号和密码的必填验证，密码最少6位
- **响应式设计**: 适配移动端和桌面端
- **API集成**: 使用真实的登录API `/esg-ims-vue/portalApi/ims-app/auth/loginOEE.action`

### 2. 用户状态管理 (`src/store/modules/user.js`)

- **Pinia Store**: 使用Pinia进行用户状态管理
- **持久化**: 用户信息本地存储，刷新页面不丢失登录状态
- **功能方法**:
  - `login()`: 用户登录
  - `logout()`: 用户登出
  - `getUserInfo()`: 获取用户信息
  - `resetState()`: 重置状态

### 3. 认证工具函数 (`src/utils/auth.js`)

- **Token管理**: 
  - `getToken()`: 获取token
  - `setToken()`: 设置token
  - `removeToken()`: 删除token
  - `clearAuth()`: 清除所有认证信息
- **白名单检查**: `whiteListCheck()` 检查路径是否需要登录

### 4. 路由配置更新

#### 路由表 (`src/router/index.js`)
- 添加登录路由 `/login`
- 默认路由重定向到登录页面

#### 路由守卫 (`src/router/router-aop.js`)
- **登录验证**: 检查用户是否已登录
- **白名单机制**: 登录页、401、404页面无需验证
- **自动跳转**: 
  - 未登录用户访问受保护页面 → 跳转到登录页
  - 已登录用户访问登录页 → 跳转到首页

### 5. 请求拦截器更新 (`src/utils/request.js`)

- **自动添加Token**: 请求头自动携带Authorization
- **401处理**: 
  - 显示错误提示
  - 清除认证信息
  - 自动跳转到登录页

### 6. 导航栏用户功能 (`src/layout/components/Navbar/index.vue`)

- **用户信息显示**: 显示用户昵称或用户名
- **下拉菜单**: 用户操作菜单
- **登出功能**: 确认对话框 + 清除状态 + 跳转登录页

### 7. API接口 (`src/api/auth.js`)

- `login()`: 用户登录接口
- `logout()`: 用户登出接口
- `getUserInfo()`: 获取用户信息接口
- `refreshToken()`: 刷新token接口

## 配置更新

### 白名单配置 (`src/config/constant.js`)
```javascript
export const whiteList = ['/login', '/401', '/404']
```

### 路由配置
- 默认路由: `/` → `/login`
- 登录成功后跳转: `/index`

## 使用说明

### 开发环境测试

1. 启动开发服务器：
   ```bash
   npm run dev
   ```

2. 访问应用：`http://localhost:82`

3. 使用测试账号登录：
   - 账号：`admin`
   - 密码：`123456`

### 登录流程

1. **访问应用** → 自动跳转到登录页
2. **输入账号密码** → 点击登入按钮
3. **验证成功** → 跳转到主页面
4. **导航栏显示用户信息** → 可以进行登出操作

### 登出流程

1. **点击用户名下拉菜单** → 选择"退出登录"
2. **确认对话框** → 点击确定
3. **清除登录状态** → 跳转到登录页

## 安全特性

1. **Token验证**: 所有API请求自动携带token
2. **路由守卫**: 未登录用户无法访问受保护页面
3. **自动登出**: token失效时自动清除状态并跳转
4. **状态持久化**: 刷新页面保持登录状态

## 技术栈

- **Vue 3**: Composition API
- **Pinia**: 状态管理
- **Vue Router**: 路由管理
- **Element Plus**: UI组件库
- **Axios**: HTTP请求
- **js-cookie**: Cookie管理

## 后续扩展

1. **真实API集成**: 替换测试登录逻辑为真实API调用
2. **记住密码**: 添加记住密码功能
3. **多因子认证**: 支持短信验证码、邮箱验证等
4. **权限管理**: 基于角色的权限控制
5. **单点登录**: 集成SSO功能

## 注意事项

1. 当前使用测试账号，生产环境需要集成真实的认证服务
2. Token刷新机制已预留接口，需要根据后端实现进行调整
3. 用户信息持久化存储在localStorage中，敏感信息需要加密处理
