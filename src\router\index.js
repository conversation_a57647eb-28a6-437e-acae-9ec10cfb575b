import { createRouter, createWebHistory } from 'vue-router'

import Layout from '@/layout'

// 公共路由，默认页面
export const constantRoutes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login.vue'),
    meta: { title: '登录' },
  },
  {
    path: '',
    redirect: '/login',
    name: 'default',
    component: Layout,
    children: [
      {
        path: '/index',
        name: 'Index',
        component: () => import('@/views/index.vue'),
        meta: { title: 'IOE知识库' },
      },
      {
        path: '/graph',
        name: 'Graph',
        component: () => import('@/views/KnowledgeGraph/index.vue'),
        meta: { title: 'IOE知识图谱' },
      },
      {
        path: '/document',
        name: 'Document',
        component: () => import('@/views/Document/index.vue'),
        meta: { title: 'IOE资源检索' },
      },
      {
        path: '/testPwa',
        name: 'TestPWA',
        component: () => import('@/views/pwa-test.vue'),
        meta: { title: '测试PWA' },
      },
      {
        path: '/testVoice',
        name: 'TestVoice',
        component: () => import('@/views/voice-test.vue'),
        meta: { title: '测试语音输入' },
      },
    ],
  },
  {
    name: '401',
    path: '/401',
    component: () => import('@/views/error/401'),
  },
  {
    name: '404',
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/error/404'),
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition)
      return savedPosition
    return { top: 0 }
  },
})

export default router
