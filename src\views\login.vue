<script setup>
import { ElMessage } from 'element-plus'
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'

import { useUserStore } from '@/store'

const router = useRouter()
const userStore = useUserStore()

// 表单数据
const loginForm = reactive({
  username: '',
  password: '',
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入账号', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
}

const loginFormRef = ref()
const loading = ref(false)

// 登录处理
async function handleLogin() {
  try {
    await loginFormRef.value.validate()
    loading.value = true

    // 使用store进行登录
    await userStore.login({
      username: loginForm.username,
      password: loginForm.password,
    })

    ElMessage.success('登录成功')

    // 跳转到首页
    router.push('/index')
  }
  catch (error) {
    ElMessage.error(error.message || '登录失败')
  }
  finally {
    loading.value = false
  }
}

// 忘记密码
function handleForgotPassword() {
  ElMessage.info('请联系管理员重置密码')
}
</script>

<template>
  <div class="login-container">
    <!-- 头部标题 -->
    <div class="login-header">
      <h1 class="system-title">OEE</h1>
      <p class="system-subtitle">Evaluation System</p>
    </div>

    <!-- 登录表单 -->
    <div class="login-form-container">
      <h2 class="login-title">登入</h2>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="rules"
        class="login-form"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="账号"
            class="login-input"
            size="large"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="密码"
            class="login-input"
            size="large"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <div class="forgot-password">
          <span @click="handleForgotPassword">忘记密码？</span>
        </div>

        <el-form-item>
          <el-button
            class="login-button"
            :loading="loading"
            @click="handleLogin"
          >
            登入
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<style lang="less" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #E8A05D 0%, #D4935A 50%, #C08556 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  box-sizing: border-box;
  position: relative;
}

.login-header {
  text-align: center;
  margin-bottom: 80px;

  .system-title {
    font-size: 72px;
    font-weight: bold;
    color: #FFFFFF;
    margin: 0 0 8px 0;
    letter-spacing: 4px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .system-subtitle {
    font-size: 18px;
    color: #FFFFFF;
    margin: 0;
    opacity: 0.9;
    font-weight: 300;
    letter-spacing: 1px;
  }
}

.login-form-container {
  width: 100%;
  max-width: 320px;

  .login-title {
    font-size: 28px;
    color: #FFFFFF;
    text-align: center;
    margin: 0 0 40px 0;
    font-weight: 500;
  }
}

.login-form {
  :deep(.el-form-item) {
    margin-bottom: 24px;
  }

  .login-input {
    :deep(.el-input__wrapper) {
      border-radius: 25px;
      padding: 0 20px;
      height: 50px;
      border: none;
      box-shadow: none;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);

      &:hover {
        background: rgba(255, 255, 255, 0.95);
      }

      &.is-focus {
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
      }
    }

    :deep(.el-input__inner) {
      font-size: 16px;
      color: #333;
      background: transparent;

      &::placeholder {
        color: #999;
      }
    }
  }
}

.forgot-password {
  text-align: right;
  margin-bottom: 30px;

  span {
    color: #FFFFFF;
    font-size: 14px;
    cursor: pointer;
    opacity: 0.8;

    &:hover {
      opacity: 1;
      text-decoration: underline;
    }
  }
}

.login-button {
  width: 100%;
  height: 50px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  background: #4A4A4A;
  border: none;
  color: #FFFFFF;

  &:hover {
    background: #3A3A3A;
  }

  &:active {
    background: #2A2A2A;
  }

  &.is-loading {
    background: #4A4A4A;
  }
}

// 响应式设计
@media (max-width: 480px) {
  .login-container {
    padding: 20px 15px;
  }

  .login-header {
    margin-bottom: 60px;

    .system-title {
      font-size: 56px;
      letter-spacing: 2px;
    }

    .system-subtitle {
      font-size: 16px;
    }
  }

  .login-form-container {
    max-width: 280px;

    .login-title {
      font-size: 24px;
      margin-bottom: 30px;
    }
  }
}

@media (max-width: 360px) {
  .login-header {
    .system-title {
      font-size: 48px;
    }
  }
}
</style>
