<script setup>
import { ElMessage } from 'element-plus'
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'

import { useUserStore } from '@/store'

const router = useRouter()
const userStore = useUserStore()

// 表单数据
const loginForm = reactive({
  username: '',
  password: '',
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入账号', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
}

const loginFormRef = ref()
const loading = ref(false)

// 登录处理
async function handleLogin() {
  try {
    await loginFormRef.value.validate()
    loading.value = true

    // 使用store进行登录
    await userStore.login({
      username: loginForm.username,
      password: loginForm.password,
    })

    ElMessage.success('登录成功')

    // 跳转到首页
    router.push('/index')
  }
  catch (error) {
    ElMessage.error(error.message || '登录失败')
  }
  finally {
    loading.value = false
  }
}

// 忘记密码
function handleForgotPassword() {
  ElMessage.info('请联系管理员重置密码')
}
</script>

<template>
  <div class="login-container">
    <!-- 顶部橙色区域 -->
    <div class="header-section">
      <h1 class="system-title">OEE</h1>
      <p class="system-subtitle">Evaluation System</p>
    </div>

    <!-- 底部白色登录区域 -->
    <div class="login-section">
      <div class="login-form-container">
        <h2 class="login-title">登入</h2>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="rules"
          class="login-form"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="账号"
              class="login-input"
              size="large"
              clearable
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="密码"
              class="login-input"
              size="large"
              show-password
              clearable
              @keyup.enter="handleLogin"
            />
          </el-form-item>

          <div class="forgot-password">
            <span @click="handleForgotPassword">忘记密码？</span>
          </div>

          <el-form-item>
            <el-button
              class="login-button"
              :loading="loading"
              @click="handleLogin"
            >
              登入
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.login-container {
  min-height: 30vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

// 顶部橙色区域
.header-section {
  background: rgba(234, 178, 94, 1);
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  padding: 60px 0px 40px;
  text-align: left;

  .system-title {
    font-size: 72px;
    font-weight: bold;
    color: #FFFFFF;
    // margin: 0 0 8px 0;
    // letter-spacing: 4px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .system-subtitle {
    font-size: 18px;
    color: #FFFFFF;
    margin: 0;
    opacity: 0.9;
    font-weight: 300;
    letter-spacing: 1px;
    // 让E字母与OEE的第一个E对齐
    margin-left: 3rem;
  }
}

// 底部白色登录区域
.login-section {
  background: #FFFFFF;
  padding: 40px 20px 60px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.login-form-container {
  width: 100%;
  max-width: 320px;

  .login-title {
    font-size: 28px;
    color: #E8A05D;
    text-align: left;
    margin: 0 0 40px 0;
    font-weight: 500;
  }
}

.login-form {
  :deep(.el-form-item) {
    margin-bottom: 24px;
  }

  .login-input {
    :deep(.el-input__wrapper) {
      border-radius: 25px;
      padding: 0 20px;
      height: 50px;
      border: 1px solid #E5E5E5;
      box-shadow: none;
      background: #FFFFFF;

      &:hover {
        border-color: #E8A05D;
      }

      &.is-focus {
        border-color: #E8A05D;
        box-shadow: 0 0 0 2px rgba(232, 160, 93, 0.2);
      }
    }

    :deep(.el-input__inner) {
      font-size: 16px;
      color: #333;
      background: transparent;

      &::placeholder {
        color: #999;
      }
    }
  }
}

.forgot-password {
  text-align: right;
  margin-bottom: 30px;

  span {
    color: #999;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      color: #E8A05D;
      text-decoration: underline;
    }
  }
}

.login-button {
  width: 100%;
  height: 50px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  background: #4A4A4A;
  border: none;
  color: #FFFFFF;

  &:hover {
    background: #3A3A3A;
  }

  &:active {
    background: #2A2A2A;
  }

  &.is-loading {
    background: #4A4A4A;
  }
}

// 响应式设计
@media (max-width: 480px) {
  .header-section {
    padding: 40px 0px 30px;

    .system-title {
      font-size: 56px;
      letter-spacing: 2px;
    }

    .system-subtitle {
      font-size: 16px;
    }
  }

  .login-section {
    padding: 30px 15px 40px;
  }

  .login-form-container {
    max-width: 280px;

    .login-title {
      font-size: 24px;
      margin-bottom: 30px;
    }
  }
}

@media (max-width: 360px) {
  .header-section {
    .system-title {
      font-size: 48px;
    }
  }
}
</style>
