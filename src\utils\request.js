import axios from 'axios'
import { ElNotification } from 'element-plus'

import { errorMessage } from '@/config/constant.js'
import { clearAuth, getToken } from '@/utils/auth.js'
import { progressDone, progressStart } from '@/utils/progress.js'

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'

const service = axios.create({
  // 超时时间
  timeout: 10000,
})

// request拦截器
service.interceptors.request.use(
  (config) => {
    progressStart()

    // 添加token到请求头
    const token = getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 请求添加时间戳
    let { url } = config
    if (url.toString().includes('?'))
      url += `&_t=${new Date().getTime()}`
    else url += `?_t=${new Date().getTime()}`
    config.url = url

    return config
  },
  (error) => {
    progressDone()
    console.log('interceptors.request error =>', error)
    Promise.reject(error)
  },
)

// 响应拦截器
service.interceptors.response.use(
  (res) => {
    progressDone()
    const code = res.data.code || 200
    const msg = errorMessage[code] || res.data.msg || errorMessage.default

    // 401 token过期，需要重新登录
    if (code === 401) {
      ElNotification.error({
        title: `错误码：${code}`,
        duration: 4500,
        message: '无效的会话，或者会话已过期，请重新登录',
      })

      // 清除认证信息并跳转到登录页
      clearAuth()

      // 避免在登录页面重复跳转
      if (window.location.pathname !== '/login') {
        window.location.href = '/login'
      }

      return Promise.reject('无效的会话，或者会话已过期，请重新登录')
    }

    // 403 无权限提示
    if (code === 403) {
      ElNotification.error({ title: `错误码：${code}`, duration: 4500, message: '权限不足，请联系管理员处理' })
      return Promise.reject('error')
    }

    // 非200的错误返回
    if (code !== 200) {
      ElNotification.error({ title: `错误码：${code}`, duration: 4500, message: msg })
      return Promise.reject('error')
    }
    return Promise.resolve(res.data)
  },
  (error) => {
    progressDone()
    console.log('interceptors.response error =>', error)
    ElNotification.error({ title: '错误码：500', duration: 4500, message: '出现未知错误，请联系管理员处理' })
    return Promise.reject(error)
  },
)

export default service
