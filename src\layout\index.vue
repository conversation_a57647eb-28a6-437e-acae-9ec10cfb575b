<script setup>
import { storeSystem } from '@/store'

import { AppMain, Navbar, Sidebar } from './components'

const _storeSystem = storeSystem()
</script>

<template>
  <div class="layout-wrapper global-scrollbar">
    <Navbar />

    <div class="main-container" :class="{ 'sidebar-fold': _storeSystem.sidebarFold }">
      <Sidebar />
      <AppMain class="global-scrollbar" />
    </div>
  </div>
</template>

<style lang="less" scoped>
.layout-wrapper {
  width: 100%;
  height: 100%;

  .main-container {
    display: flex;
    width: 100%;
    position: relative;

    .app-main-wrapper {
      flex: 1;
    }
  }
}
</style>
