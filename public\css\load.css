.loader-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999999;
  background: #eee;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .icon {
    position: relative;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 3px solid transparent;
    border-top-color: #ccc;
    animation: spin 1s linear infinite;
    -o-animation: spin 1s linear infinite;
    -moz-animation: spin 1s linear infinite;
    -webkit-animation: spin 1s linear infinite;

    &:before {
      position: absolute;
      top: 5px;
      left: 5px;
      right: 5px;
      bottom: 5px;
      content: "";
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #999;
      animation: spin 2s linear infinite;
      -o-animation: spin 2s linear infinite;
      -moz-animation: spin 2s linear infinite;
      -webkit-animation: spin 2s linear infinite;
    }

    &:after {
      position: absolute;
      top: 15px;
      left: 15px;
      right: 15px;
      bottom: 15px;
      content: "";
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #666;
      animation: spin 3s linear infinite;
      -o-animation: spin 3s linear infinite;
      -moz-animation: spin 3s linear infinite;
      -webkit-animation: spin 3s linear infinite;
    }
  }

  .title {
    font-family: 'Open Sans', serif;
    color: #666;
    font-size: 20px;
    margin-top: 20px;
  }
}

@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

