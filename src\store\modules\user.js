import { defineStore } from 'pinia'

import { getUserInfo, login, logout } from '@/api/auth'
import { clearAuth, getToken, setToken } from '@/utils/auth'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: getToken(),
    userInfo: null,
    isLoggedIn: false,
  }),

  getters: {
    // 是否已登录
    hasToken: state => !!state.token,
    // 用户名
    username: state => state.userInfo?.username || '',
    // 用户昵称
    nickname: state => state.userInfo?.name || state.userInfo?.nickname || '',
  },

  actions: {
    // 登录
    async login(loginForm) {
      try {
        const response = await login(loginForm)

        // 根据实际API响应结构调整
        let token, userInfo

        if (response.data) {
          // 如果响应中有data字段
          token = response.data.token || response.data.accessToken || response.data.access_token
          userInfo = response.data.userInfo || response.data.user || response.data
        } else {
          // 如果响应直接是数据
          token = response.token || response.accessToken || response.access_token
          userInfo = response.userInfo || response.user || response
        }

        if (token) {
          this.token = token
          setToken(token)
          this.isLoggedIn = true

          // 如果有用户信息，保存用户信息
          if (userInfo && (userInfo.username || userInfo.name)) {
            this.userInfo = userInfo
          }
        } else {
          throw new Error('登录响应中未找到token')
        }

        return response
      }
      catch (error) {
        console.error('登录失败:', error)
        throw error
      }
    },

    // 获取用户信息
    async getUserInfo() {
      try {
        if (!this.token) {
          throw new Error('Token不存在')
        }
        
        const response = await getUserInfo()
        this.userInfo = response.data
        this.isLoggedIn = true
        
        return response
      }
      catch (error) {
        // 如果获取用户信息失败，清除token
        this.logout()
        throw error
      }
    },

    // 登出
    async logout() {
      try {
        // 调用登出接口
        if (this.token) {
          await logout()
        }
      }
      catch (error) {
        console.error('登出接口调用失败:', error)
      }
      finally {
        // 无论接口是否成功，都清除本地状态
        this.token = ''
        this.userInfo = null
        this.isLoggedIn = false
        clearAuth()
      }
    },

    // 重置状态
    resetState() {
      this.token = ''
      this.userInfo = null
      this.isLoggedIn = false
      clearAuth()
    },
  },

  // 持久化配置
  persist: {
    key: 'user-store',
    storage: localStorage,
    paths: ['token', 'userInfo', 'isLoggedIn'],
  },
})
