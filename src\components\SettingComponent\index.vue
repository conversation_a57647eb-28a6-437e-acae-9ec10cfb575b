<script setup>
import { ElMessageBox } from 'element-plus'

import { storeCache } from '@/store/index.js'

const props = defineProps({
  // 显示状态
  visible: {
    type: Boolean,
    required: false,
  },
})

const emit = defineEmits(['update:visible'])

const visible = ref(props.visible)
const _storeCache = storeCache()

watch(
  () => props.visible,
  data => (visible.value = data),
)
watch(
  () => visible.value,
  data => emit('update:visible', data),
)

// 关闭事件
function handleCloseEvent() {
  visible.value = false
}

// 重置按钮
function handleRestSettingEvent() {
  ElMessageBox.confirm('系统设置将被重置，您确定要继续吗？', '重置系统设置', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
  })
    .then(() => {
      _storeCache.cleanCache()
      handleCloseEvent()
      setTimeout(() => window.location.reload(), 500)
    })
    .catch(() => {
    })
}
</script>

<template>
  <div :class="{ active: visible }" class="settings-wrapper">
    <div class="mask" @click="handleCloseEvent" />

    <div class="container">
      <div class="header">
        <div class="title">
          设置
        </div>
        <div class="icon" @click="handleCloseEvent">
          <img src="@/assets/svg/icon-close-b.svg" alt="Close">
        </div>
      </div>

      <div class="body">
        <el-button type="primary" plain @click="handleRestSettingEvent">
          重置设置
        </el-button>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.settings-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 100;
  visibility: hidden;
  opacity: 0;
  transition: visibility 0s linear 0.3s,
  opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 5;

  &.active {
    visibility: visible;
    opacity: 1;
    transition: visibility 0s linear 0s,
    opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    .mask {
      opacity: 1;
    }

    .container {
      transform: translateX(0);
    }
  }

  .mask {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .container {
    position: absolute;
    top: 0;
    right: 0;
    width: 25%;
    min-width: 280px;
    height: 100vh;
    background: #fff;
    box-shadow: -4px 0 16px rgba(0, 0, 0, 0.1);
    transform: translateX(100%);
    will-change: transform;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 15px 10px 15px;
      border-bottom: 1px solid #eee;

      .title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      .icon {
        width: 26px;
        height: 26px;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(0, 0, 0, 0.05);
        }

        img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .body {
      padding: 10px 15px;
      overflow-y: auto;
    }
  }
}
</style>
