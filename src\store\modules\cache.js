import { defineStore } from 'pinia'

export const storeCache = defineStore('storeCache', {
  state: () => ({
    _init: false,
    documentSearchHistory: [],
  }),
  actions: {
    init() {
      if (this._init)
        return
      console.log('init store cache')
      this.cleanCache()
      this._init = true
    },
    /**
     * 清空缓存信息
     */
    cleanCache() {
      this._init = false
      this.documentSearchHistory = []
    },
  },
  persist: {
    enabled: true,
    strategies: [{ storage: localStorage }],
    deserialize: JSON.parse,
    serialize: JSON.stringify,
  },
})
